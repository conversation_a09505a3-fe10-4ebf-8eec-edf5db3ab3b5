#!/usr/bin/env python3
"""
Debug script to analyze streaming chunks and identify spacing issues.
This script will help identify where extra spaces are being added.
"""

import requests
import json
import re

def analyze_chunks():
    """
    Analyze the streaming chunks to identify spacing issues
    """
    base_url = "http://localhost:8000"  # Adjust as needed
    endpoint = f"{base_url}/api/v1/prd_upload/upload_files_stream"
    
    # Test data
    files = {
        'files': ('test.txt', 'This is a test document for AI Multilingual Voice Assistant PRD generation.', 'text/plain')
    }
    
    data = {
        'user_input': 'Generate a comprehensive PRD for AI Multilingual Voice Assistant',
        'session_id': None
    }
    
    print("🔍 Starting chunk analysis...")
    print("-" * 60)
    
    chunks = []
    full_content = ""
    
    try:
        response = requests.post(
            endpoint,
            files=files,
            data=data,
            stream=True,
            headers={
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            }
        )
        
        if response.status_code != 200:
            print(f"❌ Error: HTTP {response.status_code}")
            return
        
        chunk_count = 0
        for line in response.iter_lines(decode_unicode=True):
            if line and line.startswith('data: '):
                try:
                    json_data = line[6:]
                    data = json.loads(json_data)
                    
                    if data.get('type') == 'prd_chunk':
                        content = data.get('content', '')
                        is_final = data.get('is_final', False)
                        
                        if content and not is_final:
                            chunk_count += 1
                            chunks.append(content)
                            full_content += content
                            
                            # Analyze this chunk
                            print(f"📦 Chunk #{chunk_count}:")
                            print(f"   Content: '{content}'")
                            print(f"   Length: {len(content)}")
                            print(f"   Repr: {repr(content)}")
                            print(f"   Starts with space: {content.startswith(' ')}")
                            print(f"   Ends with space: {content.endswith(' ')}")
                            
                            # Check for problematic patterns
                            if ' ' in content and len(content) < 10:
                                print(f"   ⚠️  SHORT CHUNK WITH SPACE: Might cause word splitting")
                            
                            if re.search(r'[a-zA-Z] [a-zA-Z]', content) and len(content) < 5:
                                print(f"   ⚠️  SUSPICIOUS SPACING: Single letters with spaces")
                            
                            print()
                        
                        elif is_final:
                            print("✅ Final chunk received")
                            break
                    
                    elif data.get('type') == 'result':
                        final_result = data.get('data', {}).get('prd_result', '')
                        print("\n" + "="*60)
                        print("📋 FINAL ANALYSIS:")
                        print("="*60)
                        print(f"Total chunks: {chunk_count}")
                        print(f"Concatenated length: {len(full_content)}")
                        print(f"Final result length: {len(final_result)}")
                        print()
                        
                        # Analyze spacing issues
                        print("🔍 SPACING ANALYSIS:")
                        print("-" * 30)
                        
                        # Check for double spaces
                        double_spaces = re.findall(r'  +', final_result)
                        if double_spaces:
                            print(f"❌ Found {len(double_spaces)} instances of multiple spaces")
                            for i, match in enumerate(double_spaces[:5]):  # Show first 5
                                print(f"   {i+1}. '{match}' ({len(match)} spaces)")
                        
                        # Check for space before punctuation
                        space_before_punct = re.findall(r' [.,!?;:]', final_result)
                        if space_before_punct:
                            print(f"❌ Found {len(space_before_punct)} spaces before punctuation")
                            for i, match in enumerate(space_before_punct[:5]):
                                print(f"   {i+1}. '{match}'")
                        
                        # Check for broken words (single letters with spaces)
                        broken_words = re.findall(r'\b[a-zA-Z] [a-zA-Z]\b', final_result)
                        if broken_words:
                            print(f"❌ Found {len(broken_words)} potentially broken words")
                            for i, match in enumerate(broken_words[:10]):
                                print(f"   {i+1}. '{match}'")
                        
                        # Show first 200 characters with visible spaces
                        print(f"\n📝 First 200 characters (· = space):")
                        preview = final_result[:200].replace(' ', '·')
                        print(f"'{preview}'")
                        
                        # Show problematic sections
                        print(f"\n🎯 PROBLEMATIC SECTIONS:")
                        problematic_patterns = [
                            r'[A-Z] [A-Z]',  # Capital letters with space
                            r'[a-z] [a-z]',  # Lowercase letters with space
                            r'\([A-Z] [A-Z]',  # In parentheses
                        ]
                        
                        for pattern in problematic_patterns:
                            matches = re.findall(pattern, final_result)
                            if matches:
                                print(f"Pattern '{pattern}': {matches[:5]}")
                        
                        break
                
                except json.JSONDecodeError:
                    continue
    
    except Exception as e:
        print(f"❌ Error: {e}")

def suggest_fixes():
    """
    Suggest potential fixes based on common streaming issues
    """
    print("\n" + "="*60)
    print("💡 SUGGESTED FIXES:")
    print("="*60)
    
    fixes = [
        "1. Check if LLM is breaking words mid-stream",
        "2. Implement word boundary detection in chunk processing",
        "3. Buffer incomplete words until next chunk arrives",
        "4. Remove normalize_headings from individual chunks",
        "5. Apply text processing only to complete content",
        "6. Check for tokenization issues in the LLM response"
    ]
    
    for fix in fixes:
        print(f"   {fix}")

if __name__ == "__main__":
    print("🧪 Streaming Chunk Analyzer")
    print("=" * 60)
    analyze_chunks()
    suggest_fixes()
