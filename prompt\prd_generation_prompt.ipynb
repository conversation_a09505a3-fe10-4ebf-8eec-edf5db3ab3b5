{"cells": [{"cell_type": "code", "execution_count": 7, "id": "0b4135de", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_community.document_loaders import PyPDFLoader\n", "from langchain_community.document_loaders import Docx2txtLoader\n", "from langchain_community.document_loaders import TextLoader\n", "from PIL import Image\n", "from faster_whisper import WhisperModel\n", "import pytesseract\n", "from dotenv import load_dotenv\n", "from pathlib import Path\n", "import os\n"]}, {"cell_type": "code", "execution_count": null, "id": "2cb3d966", "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": 8, "id": "c6452f08", "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "\n", "os.environ['OPENAI_API_KEY']=os.getenv(\"OPENAI_API_KEY\")\n", "openai_api_key=os.getenv(\"OPENAI_API_KEY\")"]}, {"cell_type": "code", "execution_count": 9, "id": "29b46745", "metadata": {}, "outputs": [], "source": ["llm = ChatOpenAI(model=\"gpt-3.5-turbo\")"]}, {"cell_type": "code", "execution_count": 10, "id": "4c2e0f1e", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I assist you today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 9, 'prompt_tokens': 10, 'total_tokens': 19, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-3.5-turbo-0125', 'system_fingerprint': None, 'id': 'chatcmpl-BbjAoCgfPYRi1h7ZljaJULJUxbZuU', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None}, id='run--cd5a2a56-94e4-42d0-b7b2-2646021b6d6e-0', usage_metadata={'input_tokens': 10, 'output_tokens': 9, 'total_tokens': 19, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.invoke(\"hello budy\")"]}, {"cell_type": "code", "execution_count": 11, "id": "d7cf8679", "metadata": {}, "outputs": [], "source": ["# extractors/pdf_extractor.py\n", "from langchain_community.document_loaders import PyPDFLoader"]}, {"cell_type": "code", "execution_count": 12, "id": "720b1b3c", "metadata": {}, "outputs": [], "source": ["import openai"]}, {"cell_type": "code", "execution_count": 13, "id": "7a9ce7cf", "metadata": {}, "outputs": [], "source": ["def extract_text_from_audio_openai(file_path: str) -> str:\n", "    with open(file_path, \"rb\") as audio_file:\n", "        transcript = openai.audio.transcriptions.create(\n", "            model=\"whisper-1\",\n", "            file=audio_file,\n", "            response_format=\"text\"\n", "        )\n", "    return transcript  \n"]}, {"cell_type": "code", "execution_count": 14, "id": "973b2d5d", "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader, TextLoader\n", "from PIL import Image\n", "import pytesseract\n", "from faster_whisper import WhisperModel\n", "\n", "pytesseract.pytesseract.tesseract_cmd = r\"C:\\Program Files\\Tesseract-OCR\\tesseract.exe\"\n", "\n", "\n", "\n", "def extract_all_texts_from_folder(folder_path: str) -> str:\n", "    combined_text = \"\"\n", "\n", "    for filename in os.listdir(folder_path):\n", "        file_path = os.path.join(folder_path, filename)\n", "        if not os.path.isfile(file_path):\n", "            continue\n", "\n", "        ext = Path(file_path).suffix.lower()\n", "        text = \"\"\n", "\n", "        try:\n", "            if ext == '.pdf':\n", "                loader = PyPDFLoader(file_path)\n", "                text = \"\\n\".join([page.page_content for page in loader.load()])\n", "\n", "            elif ext == '.docx':\n", "                loader = Docx2txtLoader(file_path)\n", "                docs = loader.load()\n", "                text = \"\\n\".join([doc.page_content for doc in docs])\n", "\n", "            elif ext == '.txt':\n", "                loader = TextLoader(file_path, encoding='utf-8')\n", "                docs = loader.load()\n", "                text = \"\\n\".join([doc.page_content for doc in docs])\n", "\n", "            elif ext in ['.jpg', '.jpeg', '.png']:\n", "                image = Image.open(file_path)\n", "                text = pytesseract.image_to_string(image)\n", "\n", "            elif ext in ['.mp3', '.wav']:\n", "                \n", "                text = text = extract_text_from_audio_openai(file_path)\n", "\n", "            # If no match or text still empty\n", "            if not text:\n", "                text = \"Unsupported or unreadable file.\"\n", "\n", "        except Exception as e:\n", "            text = f\"Error extracting text from {filename}: {str(e)}\"\n", "\n", "        combined_text += f\"\\n\\n--- Start of {filename} ---\\n{text}\\n--- End of {filename} ---\\n\"\n", "\n", "    return combined_text.strip()\n"]}, {"cell_type": "code", "execution_count": 15, "id": "4670f345", "metadata": {}, "outputs": [], "source": ["text =extract_all_texts_from_folder(\"./database\")"]}, {"cell_type": "code", "execution_count": 16, "id": "0e539222", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Start of AI Project Portfolio Summary.docx ---\n", "AI Project Portfolio Summary\n", "\n", "\n", "\n", "1. Fashion Detection AI System\n", "\n", "Tech: YOLOv8, Python, OpenCV, FastAPI, Google Vision API, e-commerce APIs (Flipkart, Myntra, Amazon)\n", "\n", "\n", "Industry: E-commerce / Fashion Tech\n", "\n", "\n", "Work Description:\n", " Developed an AI-powered image recognition system to detect clothing items (e.g., shirt, pants, shoes, accessories) from celebrity or user-uploaded images. Integrated third-party e-commerce APIs to find matching products for purchase. Focused on real-time inference and high model accuracy.\n", "\n", "\n", "\n", "\n", "2. Battery Detection from E-Waste Devices\n", "\n", "Tech: YOLOv8, Python, Data Augmentation, Optimizers (SGD, Adam), Evaluation Metrics (Precision, Recall, F1)\n", "\n", "\n", "Industry: Recycling / Waste Management / Electronics\n", "\n", "\n", "Work Description:\n", " Built an object detection model to identify batteries in X-ray images of discarded electronics. Applied extensive data preprocessing and augmentation techniques to improve detection accuracy and reliability in low-resource environments.\n", "\n", "\n", "\n", "\n", "3. Customer Churn Prediction System\n", "\n", "Tech: Scikit-learn, Pandas, DVC, MLflow, FastAPI, Logistic Regression, Random Forest, Decision Trees\n", "\n", "\n", "Industry: E-commerce / SaaS\n", "\n", "\n", "Work Description:\n", " Created a predictive model to identify customers at risk of churning after 3+ months of activity. Evaluated multiple algorithms including Logistic Regression, Random Forest, and Decision Trees to identify the most performant. Implemented a retrainable architecture, DVC-based MLOps pipeline, and API endpoints for real-time prediction and feature importance insights.\n", "\n", "\n", "\n", "\n", "4. AI-Powered Customer Support Chatbot (QuickShip)\n", "\n", "Tech: OpenAI GPT-4, <PERSON><PERSON><PERSON>n, FastAPI, PostgreSQL, Retrieval-Augmented Generation (RAG)\n", "\n", "\n", "Industry: Logistics / E-commerce\n", "\n", "\n", "Work Description:\n", " Built a smart customer support assistant that could answer queries related to order tracking, shipping policies, and returns using company documentation and past customer interactions. Integrated with a database and implemented fallback suggestions if data wasn’t found.\n", "\n", "\n", "\n", "\n", "5. Similar Product Recommendation Engine\n", "\n", "Tech: ResNet-50 (CNN), Scikit-learn, TF-IDF, Cosine Similarity, Pandas, OpenCV\n", "\n", "\n", "Industry: E-commerce\n", "\n", "\n", "Work Description:\n", " Developed a product recommendation system that suggests visually or descriptively similar items. Used ResNet-50 CNN for deep visual feature extraction from product images and TF-IDF with cosine similarity for text-based matching using product metadata. Combined both to deliver multi-modal recommendations.\n", "--- End of AI Project Portfolio Summary.docx ---\n", "\n", "\n", "--- Start of Machine Learning Tutorial Python 1 What is Machine Learning.mp3 ---\n", "I'm happy to announce new series on machine learning with <PERSON>. In this tutorial we are going to look into what is machine learning. There are certain tasks where computers are really better than humans. For example if I ask you to multiply these two big numbers you will take a lot of time whereas computers can do it in less than a second. Similarly if I ask you to search through millions of records then that search operation is extremely difficult for humans whereas machines can do it in a matter of nanosecond. On the other hand there are some tasks where humans outperform machines such as driving the car. You don't see many robots driving the car on the road today. In Bay Area Google and Uber and Tesla these companies are trying driverless cars but it is still in initial phase as of 2018. So most of the cars driven on the road are still by humans. Natural language conversation is also an area where humans outperform machines. So machine learning tries to make computers better at the things where traditionally humans were outperforming machines and the way we do that is we make machines learn things like humans do. So the question is why humans are better in driving the cars and having a conversation? For that we need to understand how human brain works. Human brain consists of tiny neurons. There are billions of neurons in our brain and whenever we think or make a decision a chemical signal is generated and these tiny neurons light up. Here is a simple model of these neurons where these rounds are the neurons and the arrows are the synapses that connect these neurons. So what happens is let's say when you have a baby and baby sees a cow for the first time you will tell baby that this is a cow. So internally in her brain specific set of neurons will light up and these two output neurons each corresponds to a specific entity such as cow and a car. When you say that this is a cow these set of neurons are lighting up and the edges they become stronger. These edges are shown in yellow in a blue a dark color here and it leads to a certain output. This is like you are in a jungle and you are looking for a direction now you found a direction to reach to a specific destination. Next day when baby sees a second cow you will tell the baby that okay this is also a cow. So again same set of neurons and some additional neurons light up and these pathways become more stronger. Similarly when a baby is seeing a car you will tell her that this is a car and some other set of neurons will light up the edges will become stronger and so forth. This way you are training a baby. Okay so as a human we all know in our childhood the way we learn things are we make mistakes someone corrects the mistakes and then we learn. During the learning procedure what happens in our brain is constantly these edges and neurons are being adjusted and these new pathways are being created that leads to a certain output or a decision. Computers can also be trained in similar way where neural network models are created and they are trained with a lot of training data and the corresponding output and these neurons and these edges are adjusted and the neural network becomes trained so that later on when you give a new input to machine it can tell you the correct output. This is called deep learning and deep learning is one of the areas in machine learning. Machine learning is a quite a broader term it is a complete a science of field in computer science where we use various techniques and deep learning is one of them. There are other techniques where we use the knowledge present in available data we extract it and use that knowledge to make future decisions. So overall as I said before machine learning is a bigger area deep learning is one part of it and there are like mathematical and statistical models that we use to make predictions so that's another area. Machine learning has a big implication on our real life. One of the important features in any email account is spam filter. If you are using Google or Yahoo or any other email account you will see that they filter the spam emails automatically. The way they do it is they have seen a lot of spam emails in the past and they train machines to learn from the content of those emails and they filter it out for future. Personal assistant type of devices such as Amazon Alexa and Google home is another example they are getting increasingly better at human conversation. When you talk to them they learn your ascent, they learn your terminology and then in the future they give you an accurate reply for the question that you ask them. YouTube's recommended video is another example of machine learning where they look at your past pattern on what kind of content you are watching and based on that they will generate the suggestions. Last but not least is driverless cars. I won't be surprised if in next 10 years you will see many driverless cars on the roads because companies like Tesla, Google, Uber they're investing heavily in driverless cars and all of them are using machine learning and artificial intelligence techniques are to make cars smarter. So that's all I had for this introduction tutorial. In the next tutorial we are going to write some Python code to make some predictions using linear regression. Thanks for watching. Bye.\n", "\n", "--- End of Machine Learning Tutorial Python 1 What is Machine Learning.mp3 ---\n", "\n", "\n", "--- Start of Machine-Learning-Techniques (1).png ---\n", "Input\n", "\n", "* Stock Data\n", "\n", "* Customer\n", "Transaction Data\n", "\n", "© Streaming Data\n", "\n", "© Email text\n", "\n", ">\n", "\n", "Machine Learning\n", "Techniques\n", "\n", "Regression\n", "\n", "* Clustering\n", "Association Rule\n", "\n", "* Classification\n", "\n", ">\n", "\n", "Output\n", "\n", "* Stock Price Prediction\n", "* Market Segmentation\n", "\n", "* Recommendations\n", "Systems\n", "\n", "+ Spam Detection\n", "\n", "--- End of Machine-Learning-Techniques (1).png ---\n"]}], "source": ["print(text)"]}, {"cell_type": "markdown", "id": "d004c383", "metadata": {}, "source": ["# Prompt designing part"]}, {"cell_type": "code", "execution_count": null, "id": "d79e16ad", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "# Define the prompt template\n", "template = PromptTemplate(\n", "    input_variables=[\"extracted_text\"],\n", "    template=\"\"\"\n", "You are an expert technical business analyst. Based on the following extracted text, generate a structured and professional Project Requirement Document (PRD).\n", "\n", "Extracted Input:\n", "{extracted_text}\n", "\n", "The PRD should include the following sections:\n", "1. Project Title  \n", "2. Project Overview  \n", "3. Business Objectives  \n", "4. Functional Requirements  \n", "5. Non-Functional Requirements  \n", "6. Stakeholders  \n", "7. Timeline and Milestones  \n", "8. Assumptions and Constraints  \n", "9. Risks and Mitigation  \n", "10. Conclusion or Recommendations\n", "\n", "Be concise, accurate, and professional. Do not fabricate information not present in the extracted text.\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "# Save the template\n", "template.save(\"template.json\")\n"]}, {"cell_type": "code", "execution_count": 2, "id": "56d3b3a6", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "template = PromptTemplate(\n", "    input_variables=[\"extracted_text\", \"user_instruction\"],\n", "    template=\"\"\"\n", "You are an expert technical business analyst. Based on the following extracted text, generate a structured and professional Project Requirement Document (PRD).\n", "\n", "Extracted Input:\n", "{extracted_text}\n", "\n", "Additional User Instructions:\n", "{user_instruction}\n", "\n", "The PRD should include the following sections:\n", "1. Project Title  \n", "2. Project Overview  \n", "3. Business Objectives  \n", "4. Functional Requirements  \n", "5. Non-Functional Requirements  \n", "6. Stakeholders  \n", "7. Timeline and Milestones  \n", "8. Assumptions and Constraints  \n", "9. Risks and Mitigation  \n", "10. Conclusion or Recommendations\n", "\n", "Be concise, accurate, and professional. Do not fabricate information not present in the extracted text.\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "template.save(\"template.json\")\n"]}, {"cell_type": "code", "execution_count": 1, "id": "02f3a9e4", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "template = PromptTemplate(\n", "    input_variables=[\"extracted_text\", \"user_instruction\", \"persona\"],\n", "    template=\"\"\"\n", "{persona}\n", "\n", "You are tasked with generating a structured and professional Project Requirement Document (PRD) based on the extracted information and any optional user instructions provided.\n", "\n", "Extracted Input:\n", "{extracted_text}\n", "\n", "Additional User Instructions:\n", "{user_instruction}\n", "\n", "The PRD should include the following sections:\n", "1. Project Title  \n", "2. Project Overview  \n", "3. Business Objectives  \n", "4. Functional Requirements  \n", "5. Non-Functional Requirements  \n", "6. Stakeholders  \n", "7. Timeline and Milestones  \n", "8. Assumptions and Constraints  \n", "9. Risks and Mitigation  \n", "10. Conclusion or Recommendations\n", "\n", "Be concise, accurate, and professional. Do not fabricate information not present in the extracted text.\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "template.save(\"template.json\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c61f08d7", "metadata": {}, "outputs": [], "source": ["# ========== INTELLIGENT PRD PROMPT TEMPLATE ==========\n", "from langchain_core.prompts import PromptTemplate\n", "\n", "# Create the intelligent PRD template\n", "intelligent_prd_template = PromptTemplate(\n", "    input_variables=[\"extracted_text\", \"user_instruction\", \"persona\"],\n", "    template=\"\"\"\n", "{persona}\n", "\n", "=== INTELLIGENT COMPLEXITY ANALYSIS ===\n", "\n", "Before generating the PRD, you must first analyze the input complexity using your understanding of context, not keyword matching.\n", "\n", "INPUT TO ANALYZE:\n", "Extracted Text: \"{extracted_text}\"\n", "User Instructions: \"{user_instruction}\"\n", "\n", "ANALYSIS FRAMEWORK:\n", "Carefully evaluate these four dimensions:\n", "\n", "1. **Information Richness**: \n", "   - SPARSE: Minimal details, basic concept only\n", "   - MODERATE: Some details provided, reasonable context\n", "   - RICH: Comprehensive information with multiple aspects covered\n", "\n", "2. **Technical Complexity**:\n", "   - SIMPLE: Basic functionality, straightforward implementation\n", "   - MODERATE: Some technical challenges, integration needed\n", "   - COMPLEX: Advanced technical requirements, multiple systems/technologies\n", "\n", "3. **Business Scope**:\n", "   - NARROW: Single function/department, limited stakeholders\n", "   - MEDIUM: Multiple functions, several stakeholder groups\n", "   - BROAD: Enterprise-wide, complex stakeholder ecosystem\n", "\n", "4. **Requirement Specificity**:\n", "   - VAGUE: General ideas, unclear requirements\n", "   - MODERATE: Some specific requirements, reasonable clarity\n", "   - DETAILED: Precise requirements, clear acceptance criteria\n", "\n", "COMPLEXITY LEVEL DETERMINATION:\n", "Based on your analysis above, determine the overall complexity level:\n", "\n", "- **MINIMAL**: Mostly sparse/simple/narrow/vague characteristics\n", "- **BRIEF**: Mix of sparse-moderate characteristics\n", "- **MODERATE**: Mostly moderate characteristics across dimensions\n", "- **DETAILED**: Rich/complex/broad/detailed characteristics dominate\n", "\n", "Based on the amount of detail and information provided, choose:\n", "    A) MINIMAL - Very basic info, generate short PRD (500-800 words)\n", "    B) MODERATE - Good detail, generate medium PRD (1000-1500 words) \n", "    C) DETAILED - Rich information, generate comprehensive PRD (2000+ words)\n", "    \n", "\n", "=== PRD GENERATION INSTRUCTIONS ===\n", "\n", "STEP 1: State your complexity analysis decision clearly\n", "Example: \"COMPLEXITY ANALYSIS: This project shows MODERATE complexity due to [specific reasons]\"\n", "\n", "STEP 2: Generate PRD sections with appropriate depth based on your analysis\n", "\n", "ADAPTIVE DEPTH GUIDELINES:\n", "• **MINIMAL**: 1-2 sentences per section, stick to explicit information only\n", "• **BRIEF**: 2-4 sentences per section, minimal reasonable inference\n", "• **MODERATE**: 1-2 paragraphs per section, logical extrapolation allowed\n", "• **DETAILED**: Multiple paragraphs, comprehensive analysis and strategic recommendations\n", "\n", "SECTION STRUCTURE:\n", "Generate the following sections with depth matching your complexity analysis:\n", "\n", "1. **Project Title**\n", "   - Create clear, descriptive title reflecting actual scope\n", "\n", "2. **Project Overview** \n", "   - Summarize purpose, scope, and key context\n", "   - Depth should match information richness identified\n", "\n", "3. **Business Objectives**\n", "   - Scale based on business scope analysis\n", "   - Include strategic context if complexity warrants it\n", "\n", "4. **Functional Requirements**\n", "   - Detail level must match requirement specificity identified\n", "   - Group related requirements logically\n", "\n", "5. **Non-Functional Requirements**\n", "   - Include based on technical complexity level\n", "   - Address performance, security, scalability as relevant\n", "\n", "6. **Stakeholders**\n", "   - Detail based on business scope analysis\n", "   - Include roles, responsibilities, influence levels as appropriate\n", "\n", "7. **Timeline and Milestones**\n", "   - Provide timeline estimates matching project complexity\n", "   - Break down phases according to scope\n", "\n", "8. **Assumptions and Constraints**\n", "   - Scale with overall complexity level\n", "   - Address technical, business, and resource constraints\n", "\n", "9. **Risks and Mitigation**\n", "   - Risk assessment depth should match complexity analysis\n", "   - Include technical, business, and operational risks\n", "\n", "10. **Conclusion and Next Steps**\n", "    - Summarize key points proportional to project scope\n", "    - Provide actionable recommendations\n", "\n", "=== QUALITY STANDARDS ===\n", "\n", "✓ **Transparency**: Always show your complexity analysis reasoning\n", "✓ **Consistency**: All sections must scale together with your analysis\n", "✓ **Accuracy**: Don't fabricate details beyond reasonable professional inference\n", "✓ **Clarity**: <PERSON> uncertain information as \"To be determined based on further analysis\"\n", "✓ **Value Focus**: Prioritize meaningful content over word count\n", "✓ **Professional Tone**: Maintain quality regardless of detail level\n", "✓ **Logical Flow**: Ensure sections connect and support each other\n", "\n", "Now proceed with your analysis and PRD generation.\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "# Save the template to JSON file\n", "intelligent_prd_template.save(\"intelligent_template.json\")\n", "\n", "# Example of how to use it in your existing code\n", "def create_intelligent_template_config():\n", "    \"\"\"\n", "    Create the new template configuration that replaces your existing template.json\n", "    \"\"\"\n", "    return {\n", "        \"name\": \"intelligent_prd_generator\",\n", "        \"input_variables\": [\n", "            \"extracted_text\",\n", "            \"persona\", \n", "            \"user_instruction\"\n", "        ],\n", "        \"optional_variables\": [],\n", "        \"output_parser\": None,\n", "        \"partial_variables\": {},\n", "        \"metadata\": {\n", "            \"version\": \"2.0\",\n", "            \"approach\": \"llm_based_complexity_analysis\",\n", "            \"description\": \"Intelligent PRD generation with adaptive content depth\"\n", "        },\n", "        \"tags\": [\"prd\", \"adaptive\", \"intelligent\"],\n", "        \"template\": intelligent_prd_template.template,\n", "        \"template_format\": \"f-string\",\n", "        \"validate_template\": True,\n", "        \"_type\": \"prompt\"\n", "    }\n", "\n", "# Test the template\n", "if __name__ == \"__main__\":\n", "    # Test with sample data\n", "    test_data = {\n", "        \"extracted_text\": \"Build a mobile app for food delivery with real-time tracking\",\n", "        \"persona\": \"You are a Senior Product Manager with 8 years of experience\",\n", "        \"user_instruction\": \"Focus on MVP features\"\n", "    }\n", "    \n", "    # Create prompt\n", "    formatted_prompt = intelligent_prd_template.invoke(test_data)\n", "    print(\"Generated Prompt Length:\", len(formatted_prompt.split()))\n", "    print(\"\\nPrompt Preview:\")\n", "    print(formatted_prompt[:300] + \"...\")"]}, {"cell_type": "code", "execution_count": 9, "id": "db7ad3f7", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "# Create the intelligent PRD template\n", "intelligent_prd_template = PromptTemplate(\n", "    input_variables=[\"extracted_text\", \"user_instruction\", \"persona\", \"input_token_count\", \"complexity_level\", \"target_token_range\"],\n", "    template=\"\"\"\n", "{persona}\n", "\n", "=== INTELLIGENT COMPLEXITY ANALYSIS ===\n", "\n", "You are tasked with generating a structured and professional Project Requirement Document (PRD) based on the extracted information and any optional user instructions.\n", "\n", "Estimated Input Token Count: **{input_token_count}**  \n", "Classified Complexity Level: **{complexity_level}**  \n", "Target Output Length: **{target_token_range} tokens**\n", "\n", "INPUT TO ANALYZE:\n", "Extracted Text:\n", "\\\"\\\"\\\"{extracted_text}\\\"\\\"\\\"\n", "\n", "User Instructions:\n", "\\\"\\\"\\\"{user_instruction}\\\"\\\"\\\"\n", "\n", "=== ANALYSIS FRAMEWORK ===\n", "\n", "Evaluate the input based on the following dimensions:\n", "\n", "1. **Information Richness**:  \n", "   - SPARSE: Minimal details, basic concept only  \n", "   - MODERATE: Some details provided, reasonable context  \n", "   - RICH: Comprehensive information with multiple aspects covered\n", "\n", "2. **Technical Complexity**:  \n", "   - SIMPLE: Basic functionality, straightforward implementation  \n", "   - MODERATE: Some technical challenges, integration needed  \n", "   - COMPLEX: Advanced technical requirements, multiple systems/technologies\n", "\n", "3. **Business Scope**:  \n", "   - NARROW: Single function/department, limited stakeholders  \n", "   - MEDIUM: Multiple functions, several stakeholder groups  \n", "   - BROAD: Enterprise-wide, complex stakeholder ecosystem\n", "\n", "4. **Requirement Specificity**:  \n", "   - VAGUE: General ideas, unclear requirements  \n", "   - MODERATE: Some specific requirements, reasonable clarity  \n", "   - DETAILED: Precise requirements, clear acceptance criteria\n", "\n", "=== COMPLEXITY LEVEL DETERMINATION ===\n", "\n", "Choose the appropriate level based on the analysis:\n", "\n", "- **MINIMAL**: Sparse/Simple/Narrow/Vague  \n", "- **BRIEF**: Somewhat moderate, limited detail  \n", "- **MODERATE**: Reasonably detailed, moderate challenge  \n", "- **DETAILED**: Rich input, complex systems, multiple stakeholders\n", "\n", "=== PRD GENERATION INSTRUCTIONS ===\n", "\n", "DO NOT include COMPLEXITY ANALYSIS in the PRD.\n", "\n", "FORMAT GUIDELINES:\n", "-  **All subheadings (e.g., Develop Dockerfiles)** must be bold and placed on a **separate line**  \n", "-  **Do NOT use any bullet points** like `-`, `•`, or `*`  \n", "-  Each subheading should be followed by a full sentence or paragraph on the **next line**  \n", "-  Apply this structure consistently across **all sections**\n", "-  Responses that include markdown-style bullets will be rejected\n", "\n", "✓ Match your response to **{complexity_level}** complexity  \n", "✓ Keep your total output between **{target_token_range} tokens**\n", "✓ Output length is critical. You MUST ensure your response falls within the specified token range of {target_token_range} tokens. If your output is shorter than this, it is incomplete. \n", "Use all available insights from the input and follow the adaptive depth guidelines strictly.\n", "\n", "\n", "\n", "SECTION STRUCTURE:\n", "1. **Project Title**  \n", "2. **Project Overview**  \n", "3. **Business Objectives**  \n", "4. **Functional Requirements**  \n", "5. **Non-Functional Requirements**  \n", "6. **Stakeholders**  \n", "7. **Timeline and Milestones**  \n", "8. **Assumptions and Constraints**  \n", "9. **Risks and Mitigation**  \n", "10. **Conclusion and Next Steps**\n", "\n", "=== QUALITY STANDARDS ===\n", "\n", "✓ **Transparency**: Always explain your complexity reasoning  \n", "✓ **Consistency**: Ensure all sections match the selected complexity  \n", "✓ **Accuracy**: Do not invent facts; extrapolate only with logic  \n", "✓ **Clarity**: Mark vague or missing areas as \"To be determined\"  \n", "✓ **Professional Tone**: Use clear, concise, structured language  \n", "\n", "\n", "\n", "Now begin with your complexity analysis and proceed to PRD generation.\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "# Save the template\n", "intelligent_prd_template.save(\"prd_generation_template.json\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "6abafb05", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate,load_prompt"]}, {"cell_type": "code", "execution_count": 18, "id": "5eb2d571", "metadata": {}, "outputs": [], "source": ["templete = load_prompt(\"template.json\")"]}, {"cell_type": "code", "execution_count": 19, "id": "e601650a", "metadata": {}, "outputs": [{"data": {"text/plain": ["PromptTemplate(input_variables=['extracted_text'], input_types={}, partial_variables={}, template='\\nYou are an expert technical business analyst. Based on the following extracted text, generate a structured and professional Project Requirement Document (PRD).\\n\\nExtracted Input:\\n{extracted_text}\\n\\nThe PRD should include the following sections:\\n1. Project Title  \\n2. Project Overview  \\n3. Business Objectives  \\n4. Functional Requirements  \\n5. Non-Functional Requirements  \\n6. Stakeholders  \\n7. Timeline and Milestones  \\n8. Assumptions and Constraints  \\n9. Risks and Mitigation  \\n10. Conclusion or Recommendations\\n\\nBe concise, accurate, and professional. Do not fabricate information not present in the extracted text.\\n', validate_template=True)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["templete"]}, {"cell_type": "code", "execution_count": 20, "id": "5f020bcc", "metadata": {}, "outputs": [], "source": ["chain =template | llm"]}, {"cell_type": "code", "execution_count": 25, "id": "22d6316b", "metadata": {}, "outputs": [], "source": ["result =chain.invoke(input=text).content"]}, {"cell_type": "code", "execution_count": 26, "id": "8b92a83c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Project Requirement Document (PRD)\n", "\n", "## 1. Project Title\n", "AI Project Portfolio Summary\n", "\n", "## 2. Project Overview\n", "The AI Project Portfolio Summary encompasses various projects in the field of Artificial Intelligence (AI) aimed at different industries including E-commerce, Fashion Tech, Recycling, Waste Management, Electronics, SaaS, and Logistics. The projects focus on building AI systems and solutions to address specific business challenges and opportunities.\n", "\n", "## 3. Business Objectives\n", "- Develop AI-powered systems to improve efficiency, accuracy, and customer experience within the respective industries.\n", "- Enhance real-time inference, model accuracy, and predictive capabilities for decision-making.\n", "- Implement cutting-edge technologies and algorithms to tackle specific use cases in different sectors.\n", "\n", "## 4. Functional Requirements\n", "1. Fashion Detection AI System\n", "- Image recognition system to detect clothing items.\n", "- Integration with e-commerce APIs for product matching.\n", "\n", "2. Battery Detection from E-Waste Devices\n", "- Object detection model to identify batteries in X-ray images.\n", "- Data preprocessing and augmentation techniques for accuracy.\n", "\n", "3. Customer Churn Prediction System\n", "- Predictive model to identify at-risk customers.\n", "- Implementation of multiple algorithms for evaluation.\n", "\n", "4. AI-Powered Customer Support Chatbot (QuickShip)\n", "- Development of a customer support assistant using AI technologies.\n", "- Integration with company documentation and past interactions.\n", "\n", "5. Similar Product Recommendation Engine\n", "- Product recommendation system based on visual and text-based matching.\n", "- Utilization of deep visual feature extraction and similarity metrics.\n", "\n", "## 5. Non-Functional Requirements\n", "- High model accuracy and real-time inference for all AI systems.\n", "- Robust data preprocessing and augmentation techniques for improved results.\n", "- Scalable and retrainable architectures with API endpoints for real-time predictions.\n", "\n", "## 6. Stakeholders\n", "- Project Managers\n", "- Data Scientists\n", "- Software Developers\n", "- E-commerce Managers\n", "- Industry Experts\n", "\n", "## 7. Timeline and Milestones\n", "- Define project timelines for each AI project along with key milestones.\n", "- Allocate resources and track progress accordingly.\n", "\n", "## 8. Assumptions and Constraints\n", "- Availability of required datasets and APIs.\n", "- Access to necessary computing resources for model training and deployment.\n", "- Compliance with industry regulations and data privacy requirements.\n", "\n", "## 9. Risks and Mitigation\n", "- Potential risks include model underperformance, data security breaches, and integration challenges.\n", "- Mitigation strategies involve thorough testing, regular monitoring, and collaboration with domain experts.\n", "\n", "## 10. Conclusion or Recommendations\n", "- The AI projects outlined in the portfolio summary demonstrate the application of advanced technologies to address specific business needs.\n", "- Recommendations include continuous monitoring, evaluation, and enhancement of the AI systems to ensure optimal performance and impact.\n", "\n", "---\n", "This Project Requirement Document (PRD) provides a structured overview of the AI Project Portfolio Summary with detailed requirements, stakeholders, timelines, and risk mitigation strategies. The document serves as a comprehensive guide for implementing and managing the AI projects effectively.\n"]}], "source": ["print(result)"]}, {"cell_type": "code", "execution_count": 27, "id": "9f3e7f54", "metadata": {}, "outputs": [], "source": ["from docx import Document\n", "from datetime import datetime\n", "\n", "def save_prd_to_docx(prd_text: str, output_path: str = None):\n", "    # Generate a filename with timestamp if not provided\n", "    if output_path is None:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        output_path = f\"Generated_PRD_{timestamp}.docx\"\n", "\n", "    doc = Document()\n", "    doc.add_heading(\"Project Requirement Document\", level=0)\n", "\n", "    # Add content, split by double newlines into paragraphs\n", "    for section in prd_text.split(\"\\n\\n\"):\n", "        doc.add_paragraph(section.strip())\n", "\n", "    doc.save(output_path)\n", "    print(f\"✅ PRD saved to: {output_path}\")\n"]}, {"cell_type": "code", "execution_count": 28, "id": "c8ba4c5e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ PRD saved to: Generated_PRD_20250527_142624.docx\n"]}], "source": ["# Assume `final_prd_text` is your response from the model\n", "save_prd_to_docx(result)\n"]}, {"cell_type": "code", "execution_count": 30, "id": "611e31f5", "metadata": {}, "outputs": [], "source": ["from docx import Document\n", "from docx.shared import Pt\n", "from docx.enum.style import WD_STYLE_TYPE\n", "from datetime import datetime\n", "\n", "def save_prd_to_docx(prd_text: str, output_path: \"Generated_PRD_20250527_142624.docx\" = None):\n", "    if output_path is None:\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        output_path = f\"Generated_PRD_{timestamp}.docx\"\n", "\n", "    doc = Document()\n", "\n", "    # Title\n", "    doc.add_heading(\"Project Requirement Document\", level=0)\n", "\n", "    # Known section titles to detect and bold\n", "    section_headers = [\n", "        \"Project Title\", \"Project Overview\", \"Business Objectives\",\n", "        \"Functional Requirements\", \"Non-Functional Requirements\",\n", "        \"Stakeholders\", \"Timeline and Milestones\",\n", "        \"Assumptions and Constraints\", \"Risks and Mitigation\",\n", "        \"Conclusion\", \"Recommendations\"\n", "    ]\n", "\n", "    # Split and format each section\n", "    for section in prd_text.split(\"\\n\\n\"):\n", "        section = section.strip()\n", "        if not section:\n", "            continue\n", "\n", "        # Bold if section starts with a known header\n", "        matched = False\n", "        for header in section_headers:\n", "            if section.lower().startswith(header.lower()):\n", "                doc.add_heading(section, level=1)\n", "                matched = True\n", "                break\n", "\n", "        if not matched:\n", "            p = doc.add_paragraph(section)\n", "            p.style.font.size = Pt(11)\n", "\n", "    doc.save(output_path)\n", "    print(f\"✅ Beautified PRD saved to: {output_path}\")\n"]}, {"cell_type": "code", "execution_count": 31, "id": "c9eeaea9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Beautified PRD saved to: Generated_PRD_20250527_143331.docx\n"]}], "source": ["save_prd_to_docx(result)"]}, {"cell_type": "code", "execution_count": null, "id": "4de57ab0", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "'return' outside function (4100172831.py, line 8)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[1], line 8\u001b[1;36m\u001b[0m\n\u001b[1;33m    return rev==number\u001b[0m\n\u001b[1;37m    ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m 'return' outside function\n"]}], "source": ["number = 12321\n", "rev=0\n", "while number !=0:\n", "    rem=number%10\n", "    rev=rev*10+rem\n", "    number = number//10\n", "    return rev==number\n", "\n", "    \n", "\n", "\n", "\n", "\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "91a6ef37", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "76adcc08", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "id": "ff4b0a2e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["146\n"]}], "source": ["l=[103,20,19,3444,146]\n", "minn = min(l)\n", "# print(minn)\n", "l_max=minn\n", "i_pre =minn\n", "for i in l:\n", "    # print(i)\n", "    if i > i_pre and i > l_max:\n", "        i_pre = l_max\n", "        l_max = i\n", "    elif i > i_pre:\n", "        i_pre = i\n", "        \n", "        \n", "print(i_pre)"]}, {"cell_type": "code", "execution_count": null, "id": "ee4e1c51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["345678\n"]}], "source": ["l=[103,2324560,19,3444,146,345678]\n", "\n", "l_max = l[0]\n", "index = 0\n", "for i in range(len(l)):\n", "    if l[i]> l_max:\n", "        l_max = l[i]\n", "        index = i\n", "\n", "\n", "l[index]=min(l)\n", "l_max = l[0]\n", "for i in range(len(l)):\n", "    if l[i]> l_max:\n", "        l_max = l[i]\n", "  \n", "\n", "print(l_max)"]}, {"cell_type": "code", "execution_count": 20, "id": "38b9876e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["345678\n"]}], "source": ["l=[103,2324560,19,3444,146,345678]\n", "\n", "l_max = l[0]\n", "index = 0\n", "for i in range(len(l)):\n", "    if l[i]> l_max:\n", "        l_max = l[i]\n", "        index = i\n", "\n", "l_sec = l[0]\n", "\n", "for i in range(len(l)):\n", "    if l[i]> l_sec and l[i]!=l_max:\n", "        l_sec = l[i]\n", "        \n", "\n", "print(l_sec)"]}, {"cell_type": "code", "execution_count": 17, "id": "73d3601b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📄 Tables in public schema:\n", "- chat_sessions\n", "- subscriptions\n", "- api_keys\n", "- revoked_tokens\n", "- guest_usage\n", "- plans\n", "- users\n", "- payments\n", "- chat_history\n", "- free_uploads\n", "- user_uploads\n", "- alembic_version\n"]}], "source": ["import psycopg2\n", "\n", "DATABASE_URL = \"**************************************************************"\n", "\n", "try:\n", "    conn = psycopg2.connect(DATABASE_URL)\n", "    cursor = conn.cursor()\n", "\n", "    # Check if table exists\n", "    # cursor.execute(\"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'chat_messages';\")\n", "    # columns = cursor.fetchall()\n", "    cursor.execute(\"SELECT table_name FROM information_schema.tables WHERE table_schema='public';\")\n", "    tables = cursor.fetchall()\n", "    print(\"📄 Tables in public schema:\")\n", "    for t in tables:\n", "        print(\"-\", t[0])\n", "\n", "    # print(\"✅ Connected to DB. Table 'chat_messages' has the following columns:\")\n", "    # for col in columns:\n", "    #     print(f\"- {col[0]} ({col[1]})\")\n", "\n", "except Exception as e:\n", "    print(\"❌ Failed to connect or fetch schema:\", e)\n", "finally:\n", "    if 'cursor' in locals():\n", "        cursor.close()\n", "    if 'conn' in locals():\n", "        conn.close()\n"]}, {"cell_type": "code", "execution_count": 18, "id": "8702dc8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["id → integer\n", "created_at → timestamp without time zone\n", "session_id → character varying\n", "role → character varying\n", "message → character varying\n", "chat_id → character varying\n"]}], "source": ["import psycopg2\n", "\n", "# Define your connection details\n", "conn = psycopg2.connect(\n", "    dbname=\"prd_db\",\n", "    user=\"prd_user\",\n", "    password=\"HGT765435hgf\",\n", "    host=\"**************\",\n", "    port=\"5432\"\n", ")\n", "\n", "cur = conn.cursor()\n", "cur.execute(\"\"\"\n", "    SELECT column_name, data_type \n", "    FROM information_schema.columns \n", "    WHERE table_name = 'chat_history';\n", "\"\"\")\n", "\n", "columns = cur.fetchall()\n", "\n", "for name, dtype in columns:\n", "    print(f\"{name} → {dtype}\")\n", "\n", "cur.close()\n", "conn.close()\n"]}, {"cell_type": "code", "execution_count": 19, "id": "ec87afce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📜 Chat History for session_id = 'session_001':\n", "\n", "2025-06-05 18:08:57.062680 | 🧑 Human: \n", "You are an expert technical business analyst. Based on the following extracted text, generate a structured and professional Project Requirement Document (PRD).\n", "\n", "Extracted Input:\n", "--- Start of AI Project Portfolio Summary.docx ---\n", "AI Project Portfolio Summary\n", "\n", "\n", "\n", "1. Fashion Detection AI System\n", "\n", "Tech: YOLOv8, Python, OpenCV, FastAPI, Google Vision API, e-commerce APIs (Flipkart, Myntra, Amazon)\n", "\n", "\n", "Industry: E-commerce / Fashion Tech\n", "\n", "\n", "Work Description:\n", " Developed an AI-powered image recognition system to detect clothing items (e.g., shirt, pants, shoes, accessories) from celebrity or user-uploaded images. Integrated third-party e-commerce APIs to find matching products for purchase. Focused on real-time inference and high model accuracy.\n", "\n", "\n", "\n", "\n", "2. Battery Detection from E-Waste Devices\n", "\n", "Tech: YOLOv8, Python, Data Augmentation, Optimizers (SGD, Adam), Evaluation Metrics (Precision, Recall, F1)\n", "\n", "\n", "Industry: Recycling / Waste Management / Electronics\n", "\n", "\n", "Work Description:\n", " Built an object detection model to identify batteries in X-ray images of discarded electronics. Applied extensive data preprocessing and augmentation techniques to improve detection accuracy and reliability in low-resource environments.\n", "\n", "\n", "\n", "\n", "3. Customer Churn Prediction System\n", "\n", "Tech: Scikit-learn, Pandas, DVC, MLflow, FastAPI, Logistic Regression, Random Forest, Decision Trees\n", "\n", "\n", "Industry: E-commerce / SaaS\n", "\n", "\n", "Work Description:\n", " Created a predictive model to identify customers at risk of churning after 3+ months of activity. Evaluated multiple algorithms including Logistic Regression, Random Forest, and Decision Trees to identify the most performant. Implemented a retrainable architecture, DVC-based MLOps pipeline, and API endpoints for real-time prediction and feature importance insights.\n", "\n", "\n", "\n", "\n", "4. AI-Powered Customer Support Chatbot (QuickShip)\n", "\n", "Tech: OpenAI GPT-4, <PERSON><PERSON><PERSON>n, FastAPI, PostgreSQL, Retrieval-Augmented Generation (RAG)\n", "\n", "\n", "Industry: Logistics / E-commerce\n", "\n", "\n", "Work Description:\n", " Built a smart customer support assistant that could answer queries related to order tracking, shipping policies, and returns using company documentation and past customer interactions. Integrated with a database and implemented fallback suggestions if data wasn’t found.\n", "\n", "\n", "\n", "\n", "5. Similar Product Recommendation Engine\n", "\n", "Tech: ResNet-50 (CNN), Scikit-learn, TF-IDF, Cosine Similarity, Pandas, OpenCV\n", "\n", "\n", "Industry: E-commerce\n", "\n", "\n", "Work Description:\n", " Developed a product recommendation system that suggests visually or descriptively similar items. Used ResNet-50 CNN for deep visual feature extraction from product images and TF-IDF with cosine similarity for text-based matching using product metadata. Combined both to deliver multi-modal recommendations.\n", "--- End of AI Project Portfolio Summary.docx ---\n", "\n", "\n", "--- Start of Machine Learning Tutorial Python 1 What is Machine Learning.mp3 ---\n", "I'm happy to announce new series on machine learning with <PERSON>. In this tutorial we are going to look into what is machine learning. There are certain tasks where computers are really better than humans. For example if I ask you to multiply these two big numbers you will take a lot of time whereas computers can do it in less than a second. Similarly if I ask you to search through millions of records then that search operation is extremely difficult for humans whereas machines can do it in a matter of nanosecond. On the other hand there are some tasks where humans outperform machines such as driving the car. You don't see many robots driving the car on the road today. In Bay Area Google and Uber and Tesla these companies are trying driverless cars but it is still in initial phase as of 2018. So most of the cars driven on the road are still by humans. Natural language conversation is also an area where humans outperform machines. So machine learning tries to make computers better at the things where traditionally humans were outperforming machines and the way we do that is we make machines learn things like humans do. So the question is why humans are better in driving the cars and having a conversation? For that we need to understand how human brain works. Human brain consists of tiny neurons. There are billions of neurons in our brain and whenever we think or make a decision a chemical signal is generated and these tiny neurons light up. Here is a simple model of these neurons where these rounds are the neurons and the arrows are the synapses that connect these neurons. So what happens is let's say when you have a baby and baby sees a cow for the first time you will tell baby that this is a cow. So internally in her brain specific set of neurons will light up and these two output neurons each corresponds to a specific entity such as cow and a car. When you say that this is a cow these set of neurons are lighting up and the edges they become stronger. These edges are shown in yellow in a blue a dark color here and it leads to a certain output. This is like you are in a jungle and you are looking for a direction now you found a direction to reach to a specific destination. Next day when baby sees a second cow you will tell the baby that okay this is also a cow. So again same set of neurons and some additional neurons light up and these pathways become more stronger. Similarly when a baby is seeing a car you will tell her that this is a car and some other set of neurons will light up the edges will become stronger and so forth. This way you are training a baby. Okay so as a human we all know in our childhood the way we learn things are we make mistakes someone corrects the mistakes and then we learn. During the learning procedure what happens in our brain is constantly these edges and neurons are being adjusted and these new pathways are being created that leads to a certain output or a decision. Computers can also be trained in similar way where neural network models are created and they are trained with a lot of training data and the corresponding output and these neurons and these edges are adjusted and the neural network becomes trained so that later on when you give a new input to machine it can tell you the correct output. This is called deep learning and deep learning is one of the areas in machine learning. Machine learning is a quite a broader term it is a complete a science of field in computer science where we use various techniques and deep learning is one of them. There are other techniques where we use the knowledge present in available data we extract it and use that knowledge to make future decisions. So overall as I said before machine learning is a bigger area deep learning is one part of it and there are like mathematical and statistical models that we use to make predictions so that's another area. Machine learning has a big implication on our real life. One of the important features in any email account is spam filter. If you are using Google or Yahoo or any other email account you will see that they filter the spam emails automatically. The way they do it is they have seen a lot of spam emails in the past and they train machines to learn from the content of those emails and they filter it out for future. Personal assistant type of devices such as Amazon Alexa and Google home is another example they are getting increasingly better at human conversation. When you talk to them they learn your ascent, they learn your terminology and then in the future they give you an accurate reply for the question that you ask them. YouTube's recommended video is another example of machine learning where they look at your past pattern on what kind of content you are watching and based on that they will generate the suggestions. Last but not least is driverless cars. I won't be surprised if in next 10 years you will see many driverless cars on the roads because companies like Tesla, Google, Uber they're investing heavily in driverless cars and all of them are using machine learning and artificial intelligence techniques are to make cars smarter. So that's all I had for this introduction tutorial. In the next tutorial we are going to write some Python code to make some predictions using linear regression. Thanks for watching. Bye.\n", "\n", "--- End of Machine Learning Tutorial Python 1 What is Machine Learning.mp3 ---\n", "\n", "\n", "--- Start of Machine-Learning-Techniques (1).png ---\n", "Error extracting text from Machine-Learning-Techniques (1).png: tesseract is not installed or it's not in your PATH. See README file for more information.\n", "--- End of Machine-Learning-Techniques (1).png ---\n", "\n", "The PRD should include the following sections:\n", "1. Project Title  \n", "2. Project Overview  \n", "3. Business Objectives  \n", "4. Functional Requirements  \n", "5. Non-Functional Requirements  \n", "6. Stakeholders  \n", "7. Timeline and Milestones  \n", "8. Assumptions and Constraints  \n", "9. Risks and Mitigation  \n", "10. Conclusion or Recommendations\n", "\n", "Be concise, accurate, and professional. Do not fabricate information not present in the extracted text.\n", "\n", "2025-06-05 18:08:57.062680 | 🤖 AI: **Project Requirement Document (PRD)**\n", "\n", "**1. Project Title**\n", "AI Project Portfolio Summary\n", "\n", "**2. Project Overview**\n", "The AI Project Portfolio Summary encompasses a collection of diverse AI projects, ranging from fashion detection and customer churn prediction to AI-powered chatbots and recommendation engines. Each project is tailored to address specific industry needs using advanced technologies and methodologies.\n", "\n", "**3. Business Objectives**\n", "- Develop innovative AI solutions to enhance e-commerce, recycling, waste management, customer service, and product recommendation processes.\n", "- Increase efficiency, accuracy, and customer satisfaction through the implementation of AI technologies.\n", "- Provide real-time insights, predictions, and recommendations to drive business growth and operational excellence.\n", "\n", "**4. Functional Requirements**\n", "- Implement AI-powered image recognition systems for fashion detection and battery identification.\n", "- Develop predictive models for customer churn prediction and personalized product recommendations.\n", "- Integrate third-party APIs for e-commerce product matching and database interactions.\n", "- Create smart chatbots for customer support and implement multi-modal recommendation engines.\n", "\n", "**5. Non-Functional Requirements**\n", "- Ensure high model accuracy, real-time performance, and data security across all projects.\n", "- Optimize model training and inference processes for efficiency and scalability.\n", "- Implement user-friendly interfaces for seamless interactions and intuitive customer experiences.\n", "\n", "**6. Stakeholders**\n", "- Project Team: AI developers, data scientists, project managers, and domain experts.\n", "- Business Stakeholders: Executives, product managers, marketing teams, and IT personnel.\n", "- End Users: Customers, internal staff, and stakeholders involved in the AI projects.\n", "\n", "**7. Timeline and Milestones**\n", "- Fashion Detection AI System: Completed\n", "- Battery Detection from E-Waste Devices: In progress\n", "- Customer Churn Prediction System: Upcoming\n", "- AI-Powered Customer Support Chatbot (QuickShip): Planned\n", "- Similar Product Recommendation Engine: Scheduled\n", "\n", "**8. Assumptions and Constraints**\n", "- Availability of required datasets for model training and validation.\n", "- Access to necessary APIs and tools for seamless integration.\n", "- Budgetary constraints and resource limitations may impact project timelines.\n", "\n", "**9. Risks and Mitigation**\n", "- Data privacy and compliance risks: Ensure strict adherence to data protection regulations and industry standards.\n", "- Model performance and accuracy risks: Conduct rigorous testing and validation to mitigate potential errors.\n", "- Integration and deployment risks: Establish thorough testing protocols and contingency plans for seamless implementation.\n", "\n", "**10. Conclusion or Recommendations**\n", "The AI Project Portfolio showcases a strategic approach to leveraging AI technologies across various industries to drive innovation, improve processes, and enhance customer experiences. By addressing the identified requirements and engaging stakeholders effectively, each project aims to deliver tangible value and competitive advantage to the business. Continuous monitoring, evaluation, and refinement are recommended to ensure the success of these AI initiatives.\n"]}], "source": ["import psycopg2\n", "\n", "DATABASE_URL = \"******************************************************/prd_db\"\n", "session_id = \"session_001\"# Replace with actual session_id you want to query\n", "\n", "try:\n", "    conn = psycopg2.connect(DATABASE_URL)\n", "    cursor = conn.cursor()\n", "\n", "    cursor.execute(\"\"\"\n", "        SELECT role, message, created_at FROM chat_history\n", "        WHERE session_id = %s\n", "        ORDER BY created_at ASC;\n", "    \"\"\", (session_id,))\n", "\n", "    rows = cursor.fetchall()\n", "\n", "    print(f\"\\n📜 Chat History for session_id = '{session_id}':\\n\")\n", "    for role, message, created_at in rows:\n", "        role_label = \"🧑 Human\" if role == \"human\" else \"🤖 AI\"\n", "        print(f\"{created_at} | {role_label}: {message}\")\n", "\n", "except Exception as e:\n", "    print(\"⚠️ Error reading chat history from DB:\", e)\n", "finally:\n", "    if conn:\n", "        cursor.close()\n", "        conn.close()\n"]}, {"cell_type": "code", "execution_count": 20, "id": "59778eb5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Available session IDs:\n", "- e5b44f3d-b28c-4e92-89e1-64a389e51ac6\n", "- 00b8d65b-ecfd-409a-81ee-8a2dafb35e83\n", "- b8d68d85-1751-44a9-a9c2-81ccd1e104e8\n", "- session_001\n"]}], "source": ["import psycopg2\n", "\n", "DATABASE_URL = \"******************************************************/prd_db\"\n", "\n", "try:\n", "    # Reconnect to DB\n", "    conn = psycopg2.connect(DATABASE_URL)\n", "    cursor = conn.cursor()\n", "\n", "    # Get all unique session_ids\n", "    cursor.execute(\"SELECT DISTINCT session_id FROM chat_history;\")\n", "    print(\"✅ Available session IDs:\")\n", "    for row in cursor.fetchall():\n", "        print(\"-\", row[0])\n", "\n", "    cursor.close()\n", "    conn.close()\n", "\n", "except Exception as e:\n", "    print(\"❌ Error:\", e)\n"]}, {"cell_type": "code", "execution_count": 23, "id": "9d839a63", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📜 Full Chat History for session_id = 'session_001':\n", "\n", "👤 Human [2025-06-05 18:08:57.062680]:\n", "\n", "You are an expert technical business analyst. Based on the following extracted text, generate a structured and professional Project Requirement Document (PRD).\n", "\n", "Extracted Input:\n", "--- Start of AI Project Portfolio Summary.docx ---\n", "AI Project Portfolio Summary\n", "\n", "\n", "\n", "1. Fashion Detection AI System\n", "\n", "Tech: YOLOv8, Python, OpenCV, FastAPI, Google Vision API, e-commerce APIs (Flipkart, Myntra, Amazon)\n", "\n", "\n", "Industry: E-commerce / Fashion Tech\n", "\n", "\n", "Work Description:\n", " Developed an AI-powered image recognition system to detect clothing items (e.g., shirt, pants, shoes, accessories) from celebrity or user-uploaded images. Integrated third-party e-commerce APIs to find matching products for purchase. Focused on real-time inference and high model accuracy.\n", "\n", "\n", "\n", "\n", "2. Battery Detection from E-Waste Devices\n", "\n", "Tech: YOLOv8, Python, Data Augmentation, Optimizers (SGD, Adam), Evaluation Metrics (Precision, Recall, F1)\n", "\n", "\n", "Industry: Recycling / Waste Management / Electronics\n", "\n", "\n", "Work Description:\n", " Built an object detection model to identify batteries in X-ray images of discarded electronics. Applied extensive data preprocessing and augmentation techniques to improve detection accuracy and reliability in low-resource environments.\n", "\n", "\n", "\n", "\n", "3. Customer Churn Prediction System\n", "\n", "Tech: Scikit-learn, Pandas, DVC, MLflow, FastAPI, Logistic Regression, Random Forest, Decision Trees\n", "\n", "\n", "Industry: E-commerce / SaaS\n", "\n", "\n", "Work Description:\n", " Created a predictive model to identify customers at risk of churning after 3+ months of activity. Evaluated multiple algorithms including Logistic Regression, Random Forest, and Decision Trees to identify the most performant. Implemented a retrainable architecture, DVC-based MLOps pipeline, and API endpoints for real-time prediction and feature importance insights.\n", "\n", "\n", "\n", "\n", "4. AI-Powered Customer Support Chatbot (QuickShip)\n", "\n", "Tech: OpenAI GPT-4, <PERSON><PERSON><PERSON>n, FastAPI, PostgreSQL, Retrieval-Augmented Generation (RAG)\n", "\n", "\n", "Industry: Logistics / E-commerce\n", "\n", "\n", "Work Description:\n", " Built a smart customer support assistant that could answer queries related to order tracking, shipping policies, and returns using company documentation and past customer interactions. Integrated with a database and implemented fallback suggestions if data wasn’t found.\n", "\n", "\n", "\n", "\n", "5. Similar Product Recommendation Engine\n", "\n", "Tech: ResNet-50 (CNN), Scikit-learn, TF-IDF, Cosine Similarity, Pandas, OpenCV\n", "\n", "\n", "Industry: E-commerce\n", "\n", "\n", "Work Description:\n", " Developed a product recommendation system that suggests visually or descriptively similar items. Used ResNet-50 CNN for deep visual feature extraction from product images and TF-IDF with cosine similarity for text-based matching using product metadata. Combined both to deliver multi-modal recommendations.\n", "--- End of AI Project Portfolio Summary.docx ---\n", "\n", "\n", "--- Start of Machine Learning Tutorial Python 1 What is Machine Learning.mp3 ---\n", "I'm happy to announce new series on machine learning with <PERSON>. In this tutorial we are going to look into what is machine learning. There are certain tasks where computers are really better than humans. For example if I ask you to multiply these two big numbers you will take a lot of time whereas computers can do it in less than a second. Similarly if I ask you to search through millions of records then that search operation is extremely difficult for humans whereas machines can do it in a matter of nanosecond. On the other hand there are some tasks where humans outperform machines such as driving the car. You don't see many robots driving the car on the road today. In Bay Area Google and Uber and Tesla these companies are trying driverless cars but it is still in initial phase as of 2018. So most of the cars driven on the road are still by humans. Natural language conversation is also an area where humans outperform machines. So machine learning tries to make computers better at the things where traditionally humans were outperforming machines and the way we do that is we make machines learn things like humans do. So the question is why humans are better in driving the cars and having a conversation? For that we need to understand how human brain works. Human brain consists of tiny neurons. There are billions of neurons in our brain and whenever we think or make a decision a chemical signal is generated and these tiny neurons light up. Here is a simple model of these neurons where these rounds are the neurons and the arrows are the synapses that connect these neurons. So what happens is let's say when you have a baby and baby sees a cow for the first time you will tell baby that this is a cow. So internally in her brain specific set of neurons will light up and these two output neurons each corresponds to a specific entity such as cow and a car. When you say that this is a cow these set of neurons are lighting up and the edges they become stronger. These edges are shown in yellow in a blue a dark color here and it leads to a certain output. This is like you are in a jungle and you are looking for a direction now you found a direction to reach to a specific destination. Next day when baby sees a second cow you will tell the baby that okay this is also a cow. So again same set of neurons and some additional neurons light up and these pathways become more stronger. Similarly when a baby is seeing a car you will tell her that this is a car and some other set of neurons will light up the edges will become stronger and so forth. This way you are training a baby. Okay so as a human we all know in our childhood the way we learn things are we make mistakes someone corrects the mistakes and then we learn. During the learning procedure what happens in our brain is constantly these edges and neurons are being adjusted and these new pathways are being created that leads to a certain output or a decision. Computers can also be trained in similar way where neural network models are created and they are trained with a lot of training data and the corresponding output and these neurons and these edges are adjusted and the neural network becomes trained so that later on when you give a new input to machine it can tell you the correct output. This is called deep learning and deep learning is one of the areas in machine learning. Machine learning is a quite a broader term it is a complete a science of field in computer science where we use various techniques and deep learning is one of them. There are other techniques where we use the knowledge present in available data we extract it and use that knowledge to make future decisions. So overall as I said before machine learning is a bigger area deep learning is one part of it and there are like mathematical and statistical models that we use to make predictions so that's another area. Machine learning has a big implication on our real life. One of the important features in any email account is spam filter. If you are using Google or Yahoo or any other email account you will see that they filter the spam emails automatically. The way they do it is they have seen a lot of spam emails in the past and they train machines to learn from the content of those emails and they filter it out for future. Personal assistant type of devices such as Amazon Alexa and Google home is another example they are getting increasingly better at human conversation. When you talk to them they learn your ascent, they learn your terminology and then in the future they give you an accurate reply for the question that you ask them. YouTube's recommended video is another example of machine learning where they look at your past pattern on what kind of content you are watching and based on that they will generate the suggestions. Last but not least is driverless cars. I won't be surprised if in next 10 years you will see many driverless cars on the roads because companies like Tesla, Google, Uber they're investing heavily in driverless cars and all of them are using machine learning and artificial intelligence techniques are to make cars smarter. So that's all I had for this introduction tutorial. In the next tutorial we are going to write some Python code to make some predictions using linear regression. Thanks for watching. Bye.\n", "\n", "--- End of Machine Learning Tutorial Python 1 What is Machine Learning.mp3 ---\n", "\n", "\n", "--- Start of Machine-Learning-Techniques (1).png ---\n", "Error extracting text from Machine-Learning-Techniques (1).png: tesseract is not installed or it's not in your PATH. See README file for more information.\n", "--- End of Machine-Learning-Techniques (1).png ---\n", "\n", "The PRD should include the following sections:\n", "1. Project Title  \n", "2. Project Overview  \n", "3. Business Objectives  \n", "4. Functional Requirements  \n", "5. Non-Functional Requirements  \n", "6. Stakeholders  \n", "7. Timeline and Milestones  \n", "8. Assumptions and Constraints  \n", "9. Risks and Mitigation  \n", "10. Conclusion or Recommendations\n", "\n", "Be concise, accurate, and professional. Do not fabricate information not present in the extracted text.\n", "\n", "--------------------------------------------------\n", "🤖 AI [2025-06-05 18:08:57.062680]:\n", "**Project Requirement Document (PRD)**\n", "\n", "**1. Project Title**\n", "AI Project Portfolio Summary\n", "\n", "**2. Project Overview**\n", "The AI Project Portfolio Summary encompasses a collection of diverse AI projects, ranging from fashion detection and customer churn prediction to AI-powered chatbots and recommendation engines. Each project is tailored to address specific industry needs using advanced technologies and methodologies.\n", "\n", "**3. Business Objectives**\n", "- Develop innovative AI solutions to enhance e-commerce, recycling, waste management, customer service, and product recommendation processes.\n", "- Increase efficiency, accuracy, and customer satisfaction through the implementation of AI technologies.\n", "- Provide real-time insights, predictions, and recommendations to drive business growth and operational excellence.\n", "\n", "**4. Functional Requirements**\n", "- Implement AI-powered image recognition systems for fashion detection and battery identification.\n", "- Develop predictive models for customer churn prediction and personalized product recommendations.\n", "- Integrate third-party APIs for e-commerce product matching and database interactions.\n", "- Create smart chatbots for customer support and implement multi-modal recommendation engines.\n", "\n", "**5. Non-Functional Requirements**\n", "- Ensure high model accuracy, real-time performance, and data security across all projects.\n", "- Optimize model training and inference processes for efficiency and scalability.\n", "- Implement user-friendly interfaces for seamless interactions and intuitive customer experiences.\n", "\n", "**6. Stakeholders**\n", "- Project Team: AI developers, data scientists, project managers, and domain experts.\n", "- Business Stakeholders: Executives, product managers, marketing teams, and IT personnel.\n", "- End Users: Customers, internal staff, and stakeholders involved in the AI projects.\n", "\n", "**7. Timeline and Milestones**\n", "- Fashion Detection AI System: Completed\n", "- Battery Detection from E-Waste Devices: In progress\n", "- Customer Churn Prediction System: Upcoming\n", "- AI-Powered Customer Support Chatbot (QuickShip): Planned\n", "- Similar Product Recommendation Engine: Scheduled\n", "\n", "**8. Assumptions and Constraints**\n", "- Availability of required datasets for model training and validation.\n", "- Access to necessary APIs and tools for seamless integration.\n", "- Budgetary constraints and resource limitations may impact project timelines.\n", "\n", "**9. Risks and Mitigation**\n", "- Data privacy and compliance risks: Ensure strict adherence to data protection regulations and industry standards.\n", "- Model performance and accuracy risks: Conduct rigorous testing and validation to mitigate potential errors.\n", "- Integration and deployment risks: Establish thorough testing protocols and contingency plans for seamless implementation.\n", "\n", "**10. Conclusion or Recommendations**\n", "The AI Project Portfolio showcases a strategic approach to leveraging AI technologies across various industries to drive innovation, improve processes, and enhance customer experiences. By addressing the identified requirements and engaging stakeholders effectively, each project aims to deliver tangible value and competitive advantage to the business. Continuous monitoring, evaluation, and refinement are recommended to ensure the success of these AI initiatives.\n", "--------------------------------------------------\n"]}], "source": ["import psycopg2\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "\n", "DATABASE_URL = \"******************************************************/prd_db\"\n", "session_id = \"session_001\"\n", "\n", "try:\n", "    conn = psycopg2.connect(DATABASE_URL)\n", "    cursor = conn.cursor()\n", "\n", "    cursor.execute(\"\"\"\n", "        SELECT role, message, created_at\n", "        FROM chat_history\n", "        WHERE session_id = %s\n", "        ORDER BY created_at ASC;\n", "    \"\"\", (session_id,))\n", "\n", "    print(f\"\\n📜 Full Chat History for session_id = '{session_id}':\\n\")\n", "    for role, message, timestamp in cursor.fetchall():\n", "        sender = \"👤 Human\" if role == \"human\" else \"🤖 AI\"\n", "        print(f\"{sender} [{timestamp}]:\\n{message}\\n{'-'*50}\")\n", "\n", "    cursor.close()\n", "    conn.close()\n", "\n", "except Exception as e:\n", "    print(\"❌ Error while reading chat history from DB:\", e)\n"]}, {"cell_type": "code", "execution_count": 22, "id": "74941317", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Default timestamp set for 'created_at' column.\n"]}], "source": ["import psycopg2\n", "\n", "DATABASE_URL = \"******************************************************/prd_db\"\n", "\n", "try:\n", "    conn = psycopg2.connect(DATABASE_URL)\n", "    cursor = conn.cursor()\n", "\n", "    # Set DEFAULT value for created_at column\n", "    cursor.execute(\"\"\"\n", "        ALTER TABLE chat_history\n", "        ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP;\n", "    \"\"\")\n", "    conn.commit()\n", "    print(\"✅ Default timestamp set for 'created_at' column.\")\n", "\n", "except Exception as e:\n", "    print(\"❌ Error setting default for 'created_at':\", e)\n", "\n", "finally:\n", "    if cursor:\n", "        cursor.close()\n", "    if conn:\n", "        conn.close()\n"]}, {"cell_type": "code", "execution_count": 10, "id": "9230b80d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["index 0\n", "15 1\n", "index 1\n", "2 2\n", "2 1\n", "index 2\n", "45 3\n", "index 3\n", "4 4\n", "4 3\n", "[2, 4]\n"]}], "source": ["\n", "li=[]\n", "arr=[15, 2, 45, 4 , 7]\n", "for i in range(len (arr)-1):\n", "    print(\"index\", i)\n", "    print(arr[i],i+1)\n", "    if i+1==arr[i]:\n", "        print(arr[i],i)\n", "        li.append(arr[i])\n", "        \n", "print(li)        "]}, {"cell_type": "code", "execution_count": null, "id": "d10ca3a8", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_community.document_loaders import PyPDFLoader\n", "from langchain_community.document_loaders import Docx2txtLoader\n", "from langchain_community.document_loaders import TextLoader\n", "from PIL import Image\n", "import os\n", "from pathlib import Path\n", "import pytesseract\n", "from dotenv import load_dotenv\n", "import openai\n", "import moviepy.editor as mp\n", "from pydub import AudioSegment\n", "from pydub.utils import make_chunks\n", "import tempfile\n", "import shutil\n", "\n", "load_dotenv()\n", "\n", "os.environ['OPENAI_API_KEY'] = os.getenv(\"OPENAI_API_KEY\")\n", "openai_api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "\n", "def extract_audio_from_video(video_path: str, output_audio_path: str) -> str:\n", "    \"\"\"Extract audio from video file and save as MP3\"\"\"\n", "    try:\n", "        video = mp.VideoFileClip(video_path)\n", "        audio = video.audio\n", "        audio.write_audiofile(output_audio_path, verbose=False, logger=None)\n", "        video.close()\n", "        audio.close()\n", "        return output_audio_path\n", "    except Exception as e:\n", "        raise Exception(f\"Error extracting audio from video: {str(e)}\")\n", "\n", "\n", "def chunk_audio(audio_path: str, chunk_length_ms: int = 60000) -> list:\n", "    \"\"\"Split audio into chunks for processing\"\"\"\n", "    try:\n", "        audio = AudioSegment.from_file(audio_path)\n", "        chunks = make_chunks(audio, chunk_length_ms)\n", "        \n", "        chunk_paths = []\n", "        temp_dir = tempfile.mkdtemp()\n", "        \n", "        for i, chunk in enumerate(chunks):\n", "            chunk_path = os.path.join(temp_dir, f\"chunk_{i}.mp3\")\n", "            chunk.export(chunk_path, format=\"mp3\")\n", "            chunk_paths.append(chunk_path)\n", "            \n", "        return chunk_paths, temp_dir\n", "    except Exception as e:\n", "        raise Exception(f\"Error chunking audio: {str(e)}\")\n", "\n", "\n", "def transcribe_audio_chunk(chunk_path: str) -> str:\n", "    \"\"\"Transcribe a single audio chunk using OpenAI Whisper\"\"\"\n", "    try:\n", "        with open(chunk_path, \"rb\") as audio_file:\n", "            transcript = openai.audio.transcriptions.create(\n", "                model=\"whisper-1\",\n", "                file=audio_file,\n", "                response_format=\"text\"\n", "            )\n", "        return transcript\n", "    except Exception as e:\n", "        return f\"Error transcribing chunk: {str(e)}\"\n", "\n", "\n", "def extract_text_from_video(video_path: str) -> str:\n", "    \"\"\"Extract and transcribe text from video file\"\"\"\n", "    temp_audio_path = None\n", "    temp_dir = None\n", "    \n", "    try:\n", "        # Create temporary audio file\n", "        temp_audio_path = tempfile.mktemp(suffix=\".mp3\")\n", "        \n", "        # Extract audio from video\n", "        extract_audio_from_video(video_path, temp_audio_path)\n", "        \n", "        # Check if audio file is too large (OpenAI has 25MB limit)\n", "        file_size_mb = os.path.getsize(temp_audio_path) / (1024 * 1024)\n", "        \n", "        if file_size_mb > 20:  # Leave some buffer below 25MB limit\n", "            # Chunk the audio for large files\n", "            chunk_paths, temp_dir = chunk_audio(temp_audio_path, chunk_length_ms=60000)  # 1 minute chunks\n", "            \n", "            # Transcribe each chunk\n", "            transcripts = []\n", "            for i, chunk_path in enumerate(chunk_paths):\n", "                print(f\"Transcribing chunk {i+1}/{len(chunk_paths)}...\")\n", "                transcript = transcribe_audio_chunk(chunk_path)\n", "                transcripts.append(transcript)\n", "            \n", "            # Combine all transcripts\n", "            combined_transcript = \" \".join(transcripts)\n", "            \n", "        else:\n", "            # For smaller files, transcribe directly\n", "            combined_transcript = extract_text_from_audio_openai(temp_audio_path)\n", "        \n", "        # Clean and structure the transcript\n", "        cleaned_transcript = clean_transcript(combined_transcript)\n", "        \n", "        return cleaned_transcript\n", "        \n", "    except Exception as e:\n", "        return f\"Error processing video: {str(e)}\"\n", "    \n", "    finally:\n", "        # Cleanup temporary files\n", "        if temp_audio_path and os.path.exists(temp_audio_path):\n", "            os.remove(temp_audio_path)\n", "        if temp_dir and os.path.exists(temp_dir):\n", "            shutil.rmtree(temp_dir)\n", "\n", "\n", "def clean_transcript(transcript: str) -> str:\n", "    \"\"\"Clean and structure the transcript\"\"\"\n", "    # Remove extra whitespace and normalize\n", "    cleaned = \" \".join(transcript.split())\n", "    \n", "    # Add basic sentence structure\n", "    import re\n", "    \n", "    # Add periods after sentence-ending words if missing\n", "    cleaned = re.sub(r'\\b(thank you|thanks|okay|alright|right|yes|no)\\b(?!\\.)(?=\\s+[A-Z])', r'\\1.', cleaned, flags=re.IGNORECASE)\n", "    \n", "    # Ensure proper spacing after periods\n", "    cleaned = re.sub(r'\\.(?=[A-Za-z])', '. ', cleaned)\n", "    \n", "    # Remove multiple spaces\n", "    cleaned = re.sub(r'\\s+', ' ', cleaned)\n", "    \n", "    return cleaned.strip()\n", "\n", "\n", "def extract_text_from_audio_openai(file_path: str) -> str:\n", "    \"\"\"Extract text from audio file using OpenAI Whisper\"\"\"\n", "    with open(file_path, \"rb\") as audio_file:\n", "        transcript = openai.audio.transcriptions.create(\n", "            model=\"whisper-1\",\n", "            file=audio_file,\n", "            response_format=\"text\"\n", "        )\n", "    return transcript\n", "\n", "\n", "def extract_all_texts_from_folder(folder_path: str) -> str:\n", "    \"\"\"Extract text from all supported files in a folder\"\"\"\n", "    combined_text = \"\"\n", "    \n", "    # Supported video formats\n", "    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']\n", "\n", "    for filename in os.listdir(folder_path):\n", "        file_path = os.path.join(folder_path, filename)\n", "        if not os.path.isfile(file_path):\n", "            continue\n", "\n", "        ext = Path(file_path).suffix.lower()\n", "        text = \"\"\n", "\n", "        try:\n", "            print(f\"Processing {filename}...\")\n", "            \n", "            if ext == '.pdf':\n", "                loader = PyPDFLoader(file_path)\n", "                text = \"\\n\".join([page.page_content for page in loader.load()])\n", "\n", "            elif ext == '.docx':\n", "                loader = Docx2txtLoader(file_path)\n", "                docs = loader.load()\n", "                text = \"\\n\".join([doc.page_content for doc in docs])\n", "\n", "            elif ext == '.txt':\n", "                loader = TextLoader(file_path, encoding='utf-8')\n", "                docs = loader.load()\n", "                text = \"\\n\".join([doc.page_content for doc in docs])\n", "\n", "            elif ext in ['.jpg', '.jpeg', '.png']:\n", "                image = Image.open(file_path)\n", "                text = pytesseract.image_to_string(image)\n", "\n", "            elif ext in ['.mp3', '.wav']:\n", "                text = extract_text_from_audio_openai(file_path)\n", "\n", "            elif ext in video_extensions:\n", "                text = extract_text_from_video(file_path)\n", "\n", "            # If no match or text still empty\n", "            if not text:\n", "                text = \"Unsupported or unreadable file.\"\n", "\n", "        except Exception as e:\n", "            text = f\"Error extracting text from {filename}: {str(e)}\"\n", "\n", "        combined_text += f\"\\n\\n--- Start of {filename} ---\\n{text}\\n--- End of {filename} ---\\n\"\n", "        print(f\"Completed processing {filename}\")\n", "\n", "    return combined_text.strip()\n", "\n", "\n", "def generate_prd_from_transcript(transcript: str) -> str:\n", "    \"\"\"Generate a Project Requirement Document from transcript\"\"\"\n", "    \n", "    # Initialize ChatOpenAI\n", "    llm = ChatOpenAI(\n", "        model=\"gpt-4\",\n", "        temperature=0.3,\n", "        openai_api_key=openai_api_key\n", "    )\n", "    \n", "    # Create PRD generation prompt\n", "    prd_prompt = ChatPromptTemplate.from_template(\"\"\"\n", "    Based on the following transcript, generate a comprehensive Project Requirement Document (PRD).\n", "    \n", "    The PRD should include:\n", "    1. Executive Summary\n", "    2. Project Overview\n", "    3. Objectives and Goals\n", "    4. Target Audience/Users\n", "    5. Key Features and Requirements\n", "    6. Technical Requirements\n", "    7. Success Metrics\n", "    8. Timeline and Milestones\n", "    9. Risks and Mitigation\n", "    10. Conclusion\n", "    \n", "    Make sure to extract specific requirements, features, and technical details mentioned in the transcript.\n", "    Structure the document professionally and ensure it's comprehensive.\n", "    \n", "    Transcript:\n", "    {transcript}\n", "    \n", "    Generate a detailed PRD:\n", "    \"\"\")\n", "    \n", "    # Create chain\n", "    chain = prd_prompt | llm | StrOutputParser()\n", "    \n", "    # Generate PRD\n", "    prd = chain.invoke({\"transcript\": transcript})\n", "    \n", "    return prd\n", "\n", "\n", "# Example usage function\n", "def process_folder_and_generate_prd(folder_path: str, output_file: str = \"project_requirements.txt\"):\n", "    \"\"\"Process all files in folder and generate PRD\"\"\"\n", "    \n", "    print(\"Starting text extraction from all files...\")\n", "    combined_text = extract_all_texts_from_folder(folder_path)\n", "    \n", "    print(\"Generating Project Requirement Document...\")\n", "    prd = generate_prd_from_transcript(combined_text)\n", "    \n", "    # Save PRD to file\n", "    with open(output_file, 'w', encoding='utf-8') as f:\n", "        f.write(\"PROJECT REQUIREMENT DOCUMENT\\n\")\n", "        f.write(\"=\" * 50 + \"\\n\\n\")\n", "        f.write(\"EXTRACTED CONTENT:\\n\")\n", "        f.write(\"-\" * 20 + \"\\n\")\n", "        f.write(combined_text)\n", "        f.write(\"\\n\\n\" + \"=\" * 50 + \"\\n\\n\")\n", "        f.write(\"GENERATED PRD:\\n\")\n", "        f.write(\"-\" * 15 + \"\\n\")\n", "        f.write(prd)\n", "    \n", "    print(f\"PRD generated and saved to {output_file}\")\n", "    return prd\n", "\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    # Process folder containing various file types including videos\n", "    folder_path = \"path/to/your/folder\"  # Replace with actual folder path\n", "    prd = process_folder_and_generate_prd(folder_path)\n", "    print(\"Processing complete!\")"]}, {"cell_type": "markdown", "id": "e43df938", "metadata": {}, "source": ["# extract only text from video"]}, {"cell_type": "code", "execution_count": 4, "id": "ad4eed8b", "metadata": {}, "outputs": [], "source": ["import os\n", "import tempfile\n", "import shutil\n", "import subprocess  # For FFmpeg\n", "import openai\n", "from pydub import AudioSegment\n", "from pydub.utils import make_chunks\n", "from dotenv import load_dotenv\n"]}, {"cell_type": "code", "execution_count": 5, "id": "c035a594", "metadata": {}, "outputs": [], "source": ["# !pip install pydub"]}, {"cell_type": "code", "execution_count": 6, "id": "d086f60b", "metadata": {}, "outputs": [], "source": ["# Load environment variables\n", "load_dotenv()\n", "os.environ['OPENAI_API_KEY'] = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "\n", "import subprocess\n", "\n", "def extract_audio_from_video(video_path: str, output_audio_path: str) -> str:\n", "    try:\n", "        if os.path.exists(output_audio_path):\n", "            os.remove(output_audio_path)\n", "\n", "        command = [\n", "            \"ffmpeg\",\n", "            \"-i\", video_path,\n", "            \"-vn\",                     # no video\n", "            \"-acodec\", \"libmp3lame\",   # mp3 format\n", "            \"-y\",                      # overwrite output\n", "            output_audio_path\n", "        ]\n", "\n", "        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)\n", "\n", "        if result.returncode != 0:\n", "            raise RuntimeError(f\"FFmpeg error: {result.stderr}\")\n", "\n", "        return output_audio_path\n", "\n", "    except Exception as e:\n", "        raise Exception(f\"Error extracting audio using FFmpeg: {str(e)}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "c7c0b54c", "metadata": {}, "outputs": [], "source": ["def chunk_audio(audio_path: str, chunk_length_ms: int = 60000) -> tuple:\n", "    \"\"\"Split audio into chunks for processing (default: 60 seconds per chunk)\"\"\"\n", "    try:\n", "        print(\"Chunking audio for processing...\")\n", "        audio = AudioSegment.from_file(audio_path)\n", "        chunks = make_chunks(audio, chunk_length_ms)\n", "        \n", "        chunk_paths = []\n", "        temp_dir = tempfile.mkdtemp()\n", "        \n", "        for i, chunk in enumerate(chunks):\n", "            chunk_path = os.path.join(temp_dir, f\"chunk_{i:03d}.mp3\")\n", "            chunk.export(chunk_path, format=\"mp3\")\n", "            chunk_paths.append(chunk_path)\n", "            \n", "        print(f\"Audio split into {len(chunk_paths)} chunks\")\n", "        return chunk_paths, temp_dir\n", "    except Exception as e:\n", "        raise Exception(f\"Error chunking audio: {str(e)}\")\n", "\n", "\n", "def transcribe_audio_chunk(chunk_path: str) -> str:\n", "    \"\"\"Transcribe a single audio chunk using OpenAI Whisper\"\"\"\n", "    try:\n", "        with open(chunk_path, \"rb\") as audio_file:\n", "            transcript = openai.audio.transcriptions.create(\n", "                model=\"whisper-1\",\n", "                file=audio_file,\n", "                response_format=\"text\"\n", "            )\n", "        return transcript.strip()\n", "    except Exception as e:\n", "        print(f\"Error transcribing chunk {chunk_path}: {str(e)}\")\n", "        return \"\""]}, {"cell_type": "code", "execution_count": 8, "id": "8a6dff5c", "metadata": {}, "outputs": [], "source": ["def clean_and_merge_transcripts(transcripts: list) -> str:\n", "    \"\"\"Clean and merge all transcripts into a coherent text\"\"\"\n", "    # Remove empty transcripts\n", "    valid_transcripts = [t for t in transcripts if t.strip()]\n", "    \n", "    if not valid_transcripts:\n", "        return \"No valid transcripts found.\"\n", "    \n", "    # Join transcripts with space\n", "    combined_transcript = \" \".join(valid_transcripts)\n", "    \n", "    # Clean the transcript\n", "    import re\n", "    \n", "    # Remove extra whitespace and normalize\n", "    cleaned = \" \".join(combined_transcript.split())\n", "    \n", "    # Add basic sentence structure improvements\n", "    # Add periods after common sentence-ending words if missing\n", "    cleaned = re.sub(r'\\b(thank you|thanks|okay|alright|right|yes|no|well|so)\\b(?!\\.)(?=\\s+[A-Z])', r'\\1.', cleaned, flags=re.IGNORECASE)\n", "    \n", "    # Ensure proper spacing after periods\n", "    cleaned = re.sub(r'\\.(?=[A-Za-z])', '. ', cleaned)\n", "    \n", "    # Remove multiple spaces\n", "    cleaned = re.sub(r'\\s+', ' ', cleaned)\n", "    \n", "    # Capitalize first letter\n", "    if cleaned:\n", "        cleaned = cleaned[0].upper() + cleaned[1:] if len(cleaned) > 1 else cleaned.upper()\n", "    \n", "    return cleaned.strip()"]}, {"cell_type": "code", "execution_count": null, "id": "7a73526b", "metadata": {}, "outputs": [], "source": ["def extract_text_from_video(video_path: str) -> str:\n", "    \"\"\"\n", "    Main function to extract text from video file\n", "    Process: Video → Audio → Chunks → Transcription → Merge → Clean\n", "    \"\"\"\n", "    temp_audio_path = None\n", "    temp_dir = None\n", "    \n", "    try:\n", "        # Step 1: Validate video file\n", "        if not os.path.exists(video_path):\n", "            raise Exception(f\"Video file not found: {video_path}\")\n", "        \n", "        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']\n", "        if not any(video_path.lower().endswith(ext) for ext in video_extensions):\n", "            raise Exception(f\"Unsupported video format. Supported formats: {', '.join(video_extensions)}\")\n", "        \n", "        print(f\"Processing video: {video_path}\")\n", "        \n", "        # Step 2: Extract audio from video\n", "        temp_audio_path = tempfile.mktemp(suffix=\".mp3\")\n", "        extract_audio_from_video(video_path, temp_audio_path)\n", "        \n", "        # Step 3: Check file size and decide chunking strategy\n", "        file_size_mb = os.path.getsize(temp_audio_path) / (1024 * 1024)\n", "        print(f\"Audio file size: {file_size_mb:.2f} MB\")\n", "        \n", "        transcripts = []\n", "        \n", "        if file_size_mb > 20:  # OpenAI has 25MB limit, using 20MB as safe buffer\n", "            print(\"Large file detected. Using chunking strategy...\")\n", "            \n", "            # Step 4: Chunk the audio\n", "            chunk_paths, temp_dir = chunk_audio(temp_audio_path, chunk_length_ms=60000)  # 60 seconds\n", "            \n", "            # Step 5: Transcribe each chunk\n", "            print(\"Starting transcription of chunks...\")\n", "            for i, chunk_path in enumerate(chunk_paths, 1):\n", "                print(f\"Transcribing chunk {i}/{len(chunk_paths)}...\")\n", "                transcript = transcribe_audio_chunk(chunk_path)\n", "                if transcript:\n", "                    transcripts.append(transcript)\n", "                    print(f\"✓ Chunk {i} completed ({len(transcript)} characters)\")\n", "                else:\n", "                    print(f\"✗ Chunk {i} failed or empty\")\n", "            \n", "        else:\n", "            print(\"Small file detected. Direct transcription...\")\n", "            # Step 5: Direct transcription for smaller files\n", "            transcript = transcribe_audio_chunk(temp_audio_path)\n", "            if transcript:\n", "                transcripts.append(transcript)\n", "        \n", "        # Step 6: Merge and clean transcripts\n", "        print(\"Merging and cleaning transcripts...\")\n", "        final_transcript = clean_and_merge_transcripts(transcripts)\n", "        \n", "        print(f\"✓ Video processing completed! Final transcript: {len(final_transcript)} characters\")\n", "        return final_transcript\n", "        \n", "    except Exception as e:\n", "        error_msg = f\"Error processing video {video_path}: {str(e)}\"\n", "        print(error_msg)\n", "        return error_msg\n", "    \n", "    finally:\n", "        # Cleanup temporary files\n", "        if temp_audio_path and os.path.exists(temp_audio_path):\n", "            os.remove(temp_audio_path)\n", "            print(\"Temporary audio file cleaned up\")\n", "        \n", "        if temp_dir and os.path.exists(temp_dir):\n", "            shutil.rmtree(temp_dir)\n", "            print(\"Temporary chunk files cleaned up\")\n", "\n", "# transcript = extract_text_from_video(video_path)\n", "# def process_video_file(video_path: str, output_file: str = None) -> str:\n", "#     \"\"\"\n", "#     Process a video file and optionally save transcript to file\n", "    \n", "#     Args:\n", "#         video_path: Path to the video file\n", "#         output_file: Optional path to save the transcript\n", "    \n", "#     Returns:\n", "#         Extracted transcript text\n", "#     \"\"\"\n", "#     transcript = extract_text_from_video(video_path)\n", "    \n", "#     if output_file:\n", "#         try:\n", "#             with open(output_file, 'w', encoding='utf-8') as f:\n", "#                 f.write(f\"VIDEO TRANSCRIPT\\n\")\n", "#                 f.write(\"=\" * 50 + \"\\n\")\n", "#                 f.write(f\"Source: {video_path}\\n\")\n", "#                 f.write(f\"Transcript Length: {len(transcript)} characters\\n\")\n", "#                 f.write(\"=\" * 50 + \"\\n\\n\")\n", "#                 f.write(transcript)\n", "#             print(f\"Transcript saved to: {output_file}\")\n", "#         except Exception as e:\n", "#             print(f\"Error saving transcript: {str(e)}\")\n", "    \n", "#     return transcript\n"]}, {"cell_type": "code", "execution_count": 12, "id": "62495b3a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing video: video.mp4\n", "Audio file size: 15.53 MB\n", "Small file detected. Direct transcription...\n", "Merging and cleaning transcripts...\n", "✓ Video processing completed! Final transcript: 16514 characters\n", "Temporary audio file cleaned up\n"]}], "source": ["video_path=\"video.mp4\"\n", "transcript = extract_text_from_video(video_path)"]}, {"cell_type": "code", "execution_count": null, "id": "c81a8454", "metadata": {}, "outputs": [{"data": {"text/plain": ["str"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["type(transcript)"]}, {"cell_type": "code", "execution_count": 14, "id": "992d36b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Hi guys, my name is <PERSON><PERSON><PERSON> and you are welcome to my YouTube channel So. I am super happy to announce ki is YouTube channel pe we are starting a new playlist and the topic of the playlist is Agentic AI using LanGraph Ab honestly ye ek aisa topic hai jisse related mujhe countless messages aaya hain pishle 3-4 mahino me aapki side se Aap logon ne constantly mujhe ye bola ki sir please aap LanGraph ke upar ek playlist banao And mainne around 3-4 mahine pehle decide kar liya tha ki mujhe is particular topic pe ek badiya playlist banani hai And that is why main 3-4 mahino se is topic ke upar kaafi research kar raha ho Mainne bahot time le karke ek curriculum define kiya Uske baad mainne kaafi preparation ki us curriculum ke around to prepare the content Documentation mainne bahot zyada study kiya is poore process mein And finally ab 3 mahino ke baad I am confident enough ki mujhe lagta hai ki I can make a playlist on this topic So. aaj ka jo video hai wo is sense mein special hai ki agar aap is playlist ko completely follow karna chahate ho To aaj ka video dekhna bahot zaruri hai because aaj ke video mein main aapko apna complete thought process batane waal ho Ki is poore playlist ko mainne kaise plan kiya hai Main is video mein aapko poora ka poora curriculum bhi bataunga Main saath hi saath aapko prerequisites bhi bataunga Aur iske alaba aur jo doubts aapko aa sakte hain about this playlist Wo sab kuch hum is video mein discuss karenge So. let's start the video So. pehle ek baad baat karte hain why ki The reason behind starting this playlist So. is playlist ko start karne ke peechhe mere 3 primary reasons thei Theek hai main ek ek karke aapko batata hu Sabse pehla reason hai timing Mujhe aasa lagta hai ki abhi bilkul sahi time hai agentic AI padne ka Theek hai kyu because is point pe agar aap koi bhi platform open karo YouTube open karo twitter open karo instagram open karo Aapko constantly ye term sunayi dega dekhne ko milega Duniya bhar ke jitne bhi badi companies hai Un badi companies mein jitne bhi bade thought leaders hai Har koi is point pe is particular term ko hype up kar raha hai Aur mujhe aasa lagta hai ki isko hype up karna valid bhi hai Because this is going to be the next big thing in computer science Soch ke dekho 2022 mein aapka chat gpt aaya Aur chat gpt ke aane ke baad se Generative AI ka ek completely naya trajectory start ho gaya computer science mein Aur generative AI tools ab itne mature ho gaye Ki unki help se sach mein bahut powerful agents banaya jaa sakte hain In the next 5 years Aur AI agents jo honge wo future mein bahut value create karenge That is why duniya bhar ke jitne bhi Bade bade leaders hain, bade bade companies ke jo CEOs hain Aur bahut powerful position mein jo log hain They are able to anticipate ki ye ek aisi cheez hai duniya ko badal ke rakh sakte hain And that is why is point pe agar aap padhte ho Ki agentic applications kaise banaya jaate hain Toh I feel ki aap aisi position mein aajaoge Ki future mein aapki bhi bahut zyada value hogi Toh timing is the first reason Reason number 2 is demand Jaisa maine bola video ke shuruaat mein Pichle 3-4 mahinon mein Har teesra comment mere channel pe ye hi tha Ki sir please sab kuch chhodo Aap lang graph ke upar playlist banao Because industry mein bahut zyada iske baare mein baat ho rahe hai Toh aapki side se ek strong demand aaya That was the second reason And the third reason was build up Abhi tak agar aap is channel pe dekhoge Toh hum bahut sequentially cheeze cover karte aa rahe hain Aur mera mostly ye koshish rehti hai ki Main bahut organized tariqe se cheeze ache se cover karke aage badho Toh humne pehle machine learning kiya is channel pe Uske baad humne deep learning kiya Aur uske baad phir humne land chain etc start kiya Generative AI start kiya Toh is point pe I personally feel ki hum log itni padhai kar chuke hain Ki we are kind of ready to learn and understand Lang graph and how to build AI agents So. this was reason number 3 So. in teen reasons ki wajah se I feel ki we are in a position jahaan par hume Is particular playlist ko start karna chahiye Aur iske baare mein padhna chahiye Theke, ab aage badhte hain Aur discuss karte hain ki Is playlist ko start karne ke peeche mera vision kya hai Aap kabhi bhi koi bhi kaam karte ho Toh obviously uske peeche strong vision hota hai Toh main aapke saath apna vision share karna chahata ho Ki is playlist ke through Main kya achieve karna chahata ho So. agar main aapko honestly bataun Jab land graph market me aaya Aur dheere dheere aapki side se messages Mujhe aane lage ki sir land graph padhao Toh the first thing that I did Was to go on YouTube and search Ki abhi existing kya content Available hai land graph ke upar Aur maine ye notice kiya Ki do tarah ke content available the YouTube pe Pehla ek aesa content jahaan par Directly land graph ko use karke Koi project banana sikhaya jaa raha hai Ye ek type ka content tha Fir ek second type of content tha Jahaan par bahot basic Land graph ke fundamentals sikhaya jaa rahe the Aur In dono me mujhe ek flaw dikhaya diya Ki jahaan par projects banana sikhaya jaa raha tha Wahaan par fundamentals discuss nahi kiye gaya Aur jahaan par fundamentals ke upar focus kiya gaya Wo bahot Chhota video tha aur jhat se khatam ho gaya Toh in short mujhe YouTube pe Koi comprehensive playlist dikhaya hi nahi di Land graph ke upar Jisko koi agar start karke end kare Toh usko complete end to end knowledge ho jaye Agentic applications banane ka With the help of land graph Toh that's where I decided ki chalo ek aesi Playlist banate hain Jo shayad 30-40-50 videos ki hogi Aur us playlist ko Is tarike se banate hain ki Koi agar us playlist ko end to end dekhta hai Toh usko agentic applications banana bhi aaja hai Aur usko land graph ke upar bhi Pura command ho jaye Toh mere precisely 3 goals hain is poore playlist ko leh karke Mera pehla goal hai ki Main ek itni simple to understand Playlist banana chahata ho Ki koi bhi agar wo beginner bhi hai Toh wo is playlist ko Follow karke bahot asani se agentic Applications banana sikh jaye Aur usko kisi bhi tarah ke agents Banane me koi parishani na ho Second mera jo goal hai wo ye hai Ki is playlist ke through Main aapko land graph ke fundamentals Is tarike se sikhana chahata ho ki Aapko kind of strong command ho jaye Land graph ke upar And vision number 3 is Main aapko is playlist ke through Itni conceptual understanding dena chahata ho Ki kalko agar land graph Replace ho jata hai kisi doosre Naye framework se Toh bhi aap ke paas itna conceptual depth ho Ki aap bahot asani se wo naya framework Bhi seekh pao Mere actionable goals hai Jo main is playlist ke through achieve karna Chahata ho Toh humne yeh discuss kar liya ki yeh playlist Humne kyu start ki Humne yeh bhi discuss kar liya ki is playlist ko leke mere goals Kya hai, mera vision kya hai Ab mai aapko batata ho subse important jis Ki is playlist me hum kya curriculum Follow karne wale hai Ab curriculum discuss karne ke pehle main aapko Disclaimer dena chahata ho Aur disclaimer yeh hai ki jo curriculum main abhi Aapko batane wale ho yeh Toh final nahi hai Aisa ho sakta hai ki future main Aapko kuch aisi cheeze bhi padha ho Abhi curriculum main added nahi hai Aur aisa ho sakta hai ki kuch cheeze jo main abhi Discuss karun wo final playlist ka part Na ho aur uske peeche Simple reason hai AI kind of abhi Evolve kar raha hai bohot tezi se Right, har din kuch naya aa raha hai Koi purani cheeze obsolete ho raha hai Toh you never know ki Jo curriculum aapne aaj define kiya hai Main abad valid rahe na rahe Toh ye disclaimer main aapko Pehle se dena chahata ho and that is why Main aapko exact topic by topic Curriculum nahi bataunga instead Main aapko modules bataunga ki main Kis tariqe se puray curriculum ko Kaun kaun se modules main divide kiya hai Toh let's start with the first Module, the first module Is going to be on foundations Of agentic AI Toh yeh Humare puray playlist ke pehle 5-6 videos honge aur In 5-6 videos main mera goal yeh hoga ki Main aapko agentic AI Aur uske around jo bhi terms hai Uske baare mein ek Bohot in depth overview donga Thek hai, yaha par hum discuss karenge Ki agentic AI kya hota hai Agentic AI, AI agent Main kya difference hai, agentic AI Generative AI main kya difference hai Iske alawa we will discuss ki Agentic rag kya hota hai, traditional Rag, agentic rag se kaise alag hota hai Yeh saari cheeze hum yaha par Discuss karenge, yaha par hum yeh bhi Discuss karenge ki what are the top frameworks Jinki help se aap agentic AI Applications bana sakte ho, toh yeh kind of 5-6 videos honge, jinko agar aap Dekh loge, toh aapko ek bohot badiya High level overview mil jayega ki hum Is playlist mein kya cover karne wale hain Toh very important module, thek hai Uske baad aata hai module number 2 Jaha par hum start karenge Humara lang graph ka journey So. is particular module mein mera goal Jaha par hum start karenge ki main aapko Lang graph ke fundamentals padha honga Things like graph kaise build hota hai State ka concept kya hai, nodes Kya hote hain, edges kya hote hain, conditional Edges kya hote hain, looping kaise Ki jaati hai lang graph mein Yeh saari concepts hum yaha par discuss karenge Aur not only hum yeh fundamental Concepts discuss karenge but in Concepts ki help se main aapko kuch bohot Popular AI Workflows banana sikha honga, thek hai Toh yeh rahega humara module 2 ka plan Uske baad aata hai module 3 Jaha par hum advanced Lang graph concepts sikhenge Toh fundamentals ek baar agar aap Kar loge aur unki help se agar aap AI workflows banana seekh jaoge Toh aapko kaafi confidence aajayega Aur uske baad it will be the right. Time to understand Lang graph in depth So. lang graph aapko bohot tarah ke Concepts deta hai jinki help se aap Industry grade AI agents Build kar sakte ho, for example Persistence, memory ka concept Human in the loop Breakpoint, check pointers Time travel ka concept Yeh bohot advanced concepts hai jinko Aap apne AI agents me implement Karke unko truly industry grade Bana sakte ho, toh wo saari cheez Hum is particular module me cover karenge Uske baad aata hai is playlist Ka sabse interesting module Jaha par hum AI agents Banana seekhenge, so. is point tak Humne lang graph kaafi detail Me cover kar liya hoga Aur it will be the time jaha par Hum us knowledge ko use karenge To build different different kinds of AI agents, toh is particular Module me pehle me aapko theoretical Understanding dunga around AI agents What are the popular design Patterns jo industry me aaj ki date me use Ho raha hai, aur phir hum ek ek karke Alag alag type ke AI Agents banana seekhenge, we will start With the react agent Uske baad hum reflection Design pattern seekhenge Uske baad self ask with help Ek pattern hai, uske upar hum Kaam karenge, planning ke upar Kaam karenge, aur ye sab kuch Karne ke baad hum multi agent systems Bhi banana seekhenge, toh ye sab kuch Jo core hai is playlist ka, wo kind of Hum module no. 4 me cover karenge Ek baad jab ye module Aap complete kar loge, har tarah ka AI agent banana aap seekh jaoge Uske baad hum ek different Trajectory lenge, aur hum Agentic rag applications banana Toh traditional rag aapne Banana seekha tha, lang chain me Usi ka ek kind of advanced Version hai, jaha pe hum AI agent Aur rag ka concept merge karke Agentic rag banate, yaha pe Alag alag type ke architectures hai, wo hum Discuss karenge, C rag hota hai, self Rag hota hai, toh different different rag architectures Hai, wo hum yaha pe cover karenge Karenge, aur uske baad aayega Final module of the playlist Jaha pe hum productionization Seekhenge, so. abhi tak jitna Abhi humne padha hoga is playlist me, uske Help se we will build a project Aur us project ko hum kaafi Is tareeke se build karenge, ki Wo aap resume me add kar pao Interviews me logon ko dikha pao Toh that is why yaha pe hum Apne agent ko UI provide karenge Usme debugging ka support Add karenge, observability add karenge Langsmith ka integration karenge And finally usko hum Deploy karna bhi seekhenge, thek hai Toh ye 6 modules Rehne wale hai, is poore ke poore playlist me Main exact topics Abhi aapke saath discuss nahi karna chahata Because usme constantly changes Ho rahe hain, but roughly jo structure Rahega playlist ka, wo aapke screen pe Hai, thek hai, toh I hope Aapko ye curriculum pasand aara hai, jo Puri tarike se organized hai, aapko logical Lag raha hai, baaki if you have any feedbacks Aap comment me mujhe likh ke Batana, agar mujhe lagega ki aapka feedback Is really really Very solid Toh main usko integrate kar lunga Is curriculum ke andar, thek hai Ab baat kar lete hain Pre-requisites ki, ye Kaafi logon ko doubt aata hai, jab log Playlist start karte hain, ki kya hum Is playlist ko start karne ke liye ready hain ki nahi Toh is playlist ko agar aap Seekhna chahate ho, padhna chahate ho, dekhna chahate ho Toh aapko 3 cheezein aani chahiye Sabse pehli cheezein, aapko python aana chahiye Aur for a change Main ye nahi bolunga ki basic python Is playlist me aapko Thoda intermediate level python Aana chahiye, yaha pe hum kuch aasi cheezein Use karenge, jo basic python Ke andar nahi aati, will be using OOP a lot, matlab OOPS ke principle toh use hone hi hai Uske alawa Aapka typing module, pydantic Ye saari cheezein use hongi Async IO, ye bhi use hoga Toh ye saari kuch advanced concepts jo hain Python me, ye aapko aane chahiye Agar ye sab nahi aata hai aapko, toh fir Ye playlist ko bohot acche tariqe se follow Nahi kar paoge, toh this is The thing number one, the second Thing is, you should have some Familiarity with LLMs LLMs ke saath kaam karne ka thoda Idea aapko hona chahiye, toh Agar aapne mera lang chain playlist dekha hoga Toh fir ye particular cheezein aapko Problem nahi hogi, the third thing is Lang chain, lang graph Lang chain ke upar hi bana ho hai So. constantly aap is playlist me So. you will see that whenever we are writing any code Toh waha pe koi na koi dependency Lang chain ki zaroor rehegi Toh agar aapne lang chain zara bhi nahi padha hai Toh fir ye playlist aapke sir ke upar se jayegi Toh I would recommend, I will highly Recommend, ki aap ek baar mera Lang chain playlist zaroor dekho Kuch 18 videos hai us playlist me, thode lambe Lambe videos hai, but agar aap wo dekh lete ho Toh is particular playlist me aapko bohot Help ho jayegi, theke So. prerequisites point of view se Ye 3 cheezein aapko aani chahiye Ab kuch aur Important questions is point me answer karna Chahunga, pehla Ki bohot log pooch lete hain, isliye Mata raha hoon, ki total kitne number of videos Rahenge, total is playlist me Kitne videos rahenge, so. is point Pe aapko exact count nahi bata sakta But mere estimation Ke hisaab se, somewhere around 35 to 50 videos rahne wale hain Is playlist ke andar, again Since bohot tezi se Agentic AI evolve ho raha hai Lang graph me khud me bohot changes Ho rahe hain, to exact Estimate banana possible nahi hai, but ya Somewhere between 35 to 50 Itne videos rahne wale hain, theke Agla video upload Frequency kya rahegi I'll be very honest, mai Puri koshish karunga, ki mai ek Week me aapko 3 videos la karke do Theke, 3 se zyada mere liye possible Nahi hai, aur Agar kabhi mai 3 se kam banaunga Toh uske peeche kuch reason hoga Because, you know, sabki ek personal Life hoti hai, usme cheeze ho sakti hai Toh aap please usko thoda leke chalna But, mai apni side se ye Commitment aapko dena chahunga, ki I will try To upload 3 videos per Week, theke, toh baaki calculation Aap kar lo, ki what will be the timeline, kitna Time lagega, is poore playlist ko complete hone me So. ya, ye 2 questions Mujhe lagta hai, bohot log puchte hain, toh pehle se Mai answer kar deta ho, iske alawa aur koi Questions agar aapke hain, toh aap comment Puch sakte ho, mai ya Team usko answer karne ki koshish karenge Baaki, agar Aap lang graph sekhna chahate ho Toh please aap ye playlist follow karo Maine, kind of Khud se promise kiya hai, to Build, to create the best Playlist on lang graph, aur Agle 3-4 mahinon me, mai Apna 100% lagane wala hu, Us playlist ke upar, and I really hope, ki ye jo playlist banegi Future mein, aap logon ko bohot pasand Aayegi, so. super excited for the playlist I really hope aap bhi ho, agar Aapko ye video pasand aaya Aur jo hum karne wale hain, wo pasand Aaya, please is video ko aap like karo Apne friends ke saath share karo, jo bhi Lang graph padhna chahate hain, aur agar aapne Is channel ko subscribe nahi kiya hai, please Do subscribe, milte hain next video me Bye\""]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["transcript\n"]}, {"cell_type": "code", "execution_count": 15, "id": "c21d37ff", "metadata": {}, "outputs": [], "source": ["video_text=[]\n", "video_text.append(transcript)"]}, {"cell_type": "code", "execution_count": 16, "id": "96b3a34e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[\"Hi guys, my name is <PERSON><PERSON><PERSON> and you are welcome to my YouTube channel So. I am super happy to announce ki is YouTube channel pe we are starting a new playlist and the topic of the playlist is Agentic AI using LanGraph Ab honestly ye ek aisa topic hai jisse related mujhe countless messages aaya hain pishle 3-4 mahino me aapki side se Aap logon ne constantly mujhe ye bola ki sir please aap LanGraph ke upar ek playlist banao And mainne around 3-4 mahine pehle decide kar liya tha ki mujhe is particular topic pe ek badiya playlist banani hai And that is why main 3-4 mahino se is topic ke upar kaafi research kar raha ho Mainne bahot time le karke ek curriculum define kiya Uske baad mainne kaafi preparation ki us curriculum ke around to prepare the content Documentation mainne bahot zyada study kiya is poore process mein And finally ab 3 mahino ke baad I am confident enough ki mujhe lagta hai ki I can make a playlist on this topic So. aaj ka jo video hai wo is sense mein special hai ki agar aap is playlist ko completely follow karna chahate ho To aaj ka video dekhna bahot zaruri hai because aaj ke video mein main aapko apna complete thought process batane waal ho Ki is poore playlist ko mainne kaise plan kiya hai Main is video mein aapko poora ka poora curriculum bhi bataunga Main saath hi saath aapko prerequisites bhi bataunga Aur iske alaba aur jo doubts aapko aa sakte hain about this playlist Wo sab kuch hum is video mein discuss karenge So. let's start the video So. pehle ek baad baat karte hain why ki The reason behind starting this playlist So. is playlist ko start karne ke peechhe mere 3 primary reasons thei Theek hai main ek ek karke aapko batata hu Sabse pehla reason hai timing Mujhe aasa lagta hai ki abhi bilkul sahi time hai agentic AI padne ka Theek hai kyu because is point pe agar aap koi bhi platform open karo YouTube open karo twitter open karo instagram open karo Aapko constantly ye term sunayi dega dekhne ko milega Duniya bhar ke jitne bhi badi companies hai Un badi companies mein jitne bhi bade thought leaders hai Har koi is point pe is particular term ko hype up kar raha hai Aur mujhe aasa lagta hai ki isko hype up karna valid bhi hai Because this is going to be the next big thing in computer science Soch ke dekho 2022 mein aapka chat gpt aaya Aur chat gpt ke aane ke baad se Generative AI ka ek completely naya trajectory start ho gaya computer science mein Aur generative AI tools ab itne mature ho gaye Ki unki help se sach mein bahut powerful agents banaya jaa sakte hain In the next 5 years Aur AI agents jo honge wo future mein bahut value create karenge That is why duniya bhar ke jitne bhi Bade bade leaders hain, bade bade companies ke jo CEOs hain Aur bahut powerful position mein jo log hain They are able to anticipate ki ye ek aisi cheez hai duniya ko badal ke rakh sakte hain And that is why is point pe agar aap padhte ho Ki agentic applications kaise banaya jaate hain Toh I feel ki aap aisi position mein aajaoge Ki future mein aapki bhi bahut zyada value hogi Toh timing is the first reason Reason number 2 is demand Jaisa maine bola video ke shuruaat mein Pichle 3-4 mahinon mein Har teesra comment mere channel pe ye hi tha Ki sir please sab kuch chhodo Aap lang graph ke upar playlist banao Because industry mein bahut zyada iske baare mein baat ho rahe hai Toh aapki side se ek strong demand aaya That was the second reason And the third reason was build up Abhi tak agar aap is channel pe dekhoge Toh hum bahut sequentially cheeze cover karte aa rahe hain Aur mera mostly ye koshish rehti hai ki Main bahut organized tariqe se cheeze ache se cover karke aage badho Toh humne pehle machine learning kiya is channel pe Uske baad humne deep learning kiya Aur uske baad phir humne land chain etc start kiya Generative AI start kiya Toh is point pe I personally feel ki hum log itni padhai kar chuke hain Ki we are kind of ready to learn and understand Lang graph and how to build AI agents So. this was reason number 3 So. in teen reasons ki wajah se I feel ki we are in a position jahaan par hume Is particular playlist ko start karna chahiye Aur iske baare mein padhna chahiye Theke, ab aage badhte hain Aur discuss karte hain ki Is playlist ko start karne ke peeche mera vision kya hai Aap kabhi bhi koi bhi kaam karte ho Toh obviously uske peeche strong vision hota hai Toh main aapke saath apna vision share karna chahata ho Ki is playlist ke through Main kya achieve karna chahata ho So. agar main aapko honestly bataun Jab land graph market me aaya Aur dheere dheere aapki side se messages Mujhe aane lage ki sir land graph padhao Toh the first thing that I did Was to go on YouTube and search Ki abhi existing kya content Available hai land graph ke upar Aur maine ye notice kiya Ki do tarah ke content available the YouTube pe Pehla ek aesa content jahaan par Directly land graph ko use karke Koi project banana sikhaya jaa raha hai Ye ek type ka content tha Fir ek second type of content tha Jahaan par bahot basic Land graph ke fundamentals sikhaya jaa rahe the Aur In dono me mujhe ek flaw dikhaya diya Ki jahaan par projects banana sikhaya jaa raha tha Wahaan par fundamentals discuss nahi kiye gaya Aur jahaan par fundamentals ke upar focus kiya gaya Wo bahot Chhota video tha aur jhat se khatam ho gaya Toh in short mujhe YouTube pe Koi comprehensive playlist dikhaya hi nahi di Land graph ke upar Jisko koi agar start karke end kare Toh usko complete end to end knowledge ho jaye Agentic applications banane ka With the help of land graph Toh that's where I decided ki chalo ek aesi Playlist banate hain Jo shayad 30-40-50 videos ki hogi Aur us playlist ko Is tarike se banate hain ki Koi agar us playlist ko end to end dekhta hai Toh usko agentic applications banana bhi aaja hai Aur usko land graph ke upar bhi Pura command ho jaye Toh mere precisely 3 goals hain is poore playlist ko leh karke Mera pehla goal hai ki Main ek itni simple to understand Playlist banana chahata ho Ki koi bhi agar wo beginner bhi hai Toh wo is playlist ko Follow karke bahot asani se agentic Applications banana sikh jaye Aur usko kisi bhi tarah ke agents Banane me koi parishani na ho Second mera jo goal hai wo ye hai Ki is playlist ke through Main aapko land graph ke fundamentals Is tarike se sikhana chahata ho ki Aapko kind of strong command ho jaye Land graph ke upar And vision number 3 is Main aapko is playlist ke through Itni conceptual understanding dena chahata ho Ki kalko agar land graph Replace ho jata hai kisi doosre Naye framework se Toh bhi aap ke paas itna conceptual depth ho Ki aap bahot asani se wo naya framework Bhi seekh pao Mere actionable goals hai Jo main is playlist ke through achieve karna Chahata ho Toh humne yeh discuss kar liya ki yeh playlist Humne kyu start ki Humne yeh bhi discuss kar liya ki is playlist ko leke mere goals Kya hai, mera vision kya hai Ab mai aapko batata ho subse important jis Ki is playlist me hum kya curriculum Follow karne wale hai Ab curriculum discuss karne ke pehle main aapko Disclaimer dena chahata ho Aur disclaimer yeh hai ki jo curriculum main abhi Aapko batane wale ho yeh Toh final nahi hai Aisa ho sakta hai ki future main Aapko kuch aisi cheeze bhi padha ho Abhi curriculum main added nahi hai Aur aisa ho sakta hai ki kuch cheeze jo main abhi Discuss karun wo final playlist ka part Na ho aur uske peeche Simple reason hai AI kind of abhi Evolve kar raha hai bohot tezi se Right, har din kuch naya aa raha hai Koi purani cheeze obsolete ho raha hai Toh you never know ki Jo curriculum aapne aaj define kiya hai Main abad valid rahe na rahe Toh ye disclaimer main aapko Pehle se dena chahata ho and that is why Main aapko exact topic by topic Curriculum nahi bataunga instead Main aapko modules bataunga ki main Kis tariqe se puray curriculum ko Kaun kaun se modules main divide kiya hai Toh let's start with the first Module, the first module Is going to be on foundations Of agentic AI Toh yeh Humare puray playlist ke pehle 5-6 videos honge aur In 5-6 videos main mera goal yeh hoga ki Main aapko agentic AI Aur uske around jo bhi terms hai Uske baare mein ek Bohot in depth overview donga Thek hai, yaha par hum discuss karenge Ki agentic AI kya hota hai Agentic AI, AI agent Main kya difference hai, agentic AI Generative AI main kya difference hai Iske alawa we will discuss ki Agentic rag kya hota hai, traditional Rag, agentic rag se kaise alag hota hai Yeh saari cheeze hum yaha par Discuss karenge, yaha par hum yeh bhi Discuss karenge ki what are the top frameworks Jinki help se aap agentic AI Applications bana sakte ho, toh yeh kind of 5-6 videos honge, jinko agar aap Dekh loge, toh aapko ek bohot badiya High level overview mil jayega ki hum Is playlist mein kya cover karne wale hain Toh very important module, thek hai Uske baad aata hai module number 2 Jaha par hum start karenge Humara lang graph ka journey So. is particular module mein mera goal Jaha par hum start karenge ki main aapko Lang graph ke fundamentals padha honga Things like graph kaise build hota hai State ka concept kya hai, nodes Kya hote hain, edges kya hote hain, conditional Edges kya hote hain, looping kaise Ki jaati hai lang graph mein Yeh saari concepts hum yaha par discuss karenge Aur not only hum yeh fundamental Concepts discuss karenge but in Concepts ki help se main aapko kuch bohot Popular AI Workflows banana sikha honga, thek hai Toh yeh rahega humara module 2 ka plan Uske baad aata hai module 3 Jaha par hum advanced Lang graph concepts sikhenge Toh fundamentals ek baar agar aap Kar loge aur unki help se agar aap AI workflows banana seekh jaoge Toh aapko kaafi confidence aajayega Aur uske baad it will be the right. Time to understand Lang graph in depth So. lang graph aapko bohot tarah ke Concepts deta hai jinki help se aap Industry grade AI agents Build kar sakte ho, for example Persistence, memory ka concept Human in the loop Breakpoint, check pointers Time travel ka concept Yeh bohot advanced concepts hai jinko Aap apne AI agents me implement Karke unko truly industry grade Bana sakte ho, toh wo saari cheez Hum is particular module me cover karenge Uske baad aata hai is playlist Ka sabse interesting module Jaha par hum AI agents Banana seekhenge, so. is point tak Humne lang graph kaafi detail Me cover kar liya hoga Aur it will be the time jaha par Hum us knowledge ko use karenge To build different different kinds of AI agents, toh is particular Module me pehle me aapko theoretical Understanding dunga around AI agents What are the popular design Patterns jo industry me aaj ki date me use Ho raha hai, aur phir hum ek ek karke Alag alag type ke AI Agents banana seekhenge, we will start With the react agent Uske baad hum reflection Design pattern seekhenge Uske baad self ask with help Ek pattern hai, uske upar hum Kaam karenge, planning ke upar Kaam karenge, aur ye sab kuch Karne ke baad hum multi agent systems Bhi banana seekhenge, toh ye sab kuch Jo core hai is playlist ka, wo kind of Hum module no. 4 me cover karenge Ek baad jab ye module Aap complete kar loge, har tarah ka AI agent banana aap seekh jaoge Uske baad hum ek different Trajectory lenge, aur hum Agentic rag applications banana Toh traditional rag aapne Banana seekha tha, lang chain me Usi ka ek kind of advanced Version hai, jaha pe hum AI agent Aur rag ka concept merge karke Agentic rag banate, yaha pe Alag alag type ke architectures hai, wo hum Discuss karenge, C rag hota hai, self Rag hota hai, toh different different rag architectures Hai, wo hum yaha pe cover karenge Karenge, aur uske baad aayega Final module of the playlist Jaha pe hum productionization Seekhenge, so. abhi tak jitna Abhi humne padha hoga is playlist me, uske Help se we will build a project Aur us project ko hum kaafi Is tareeke se build karenge, ki Wo aap resume me add kar pao Interviews me logon ko dikha pao Toh that is why yaha pe hum Apne agent ko UI provide karenge Usme debugging ka support Add karenge, observability add karenge Langsmith ka integration karenge And finally usko hum Deploy karna bhi seekhenge, thek hai Toh ye 6 modules Rehne wale hai, is poore ke poore playlist me Main exact topics Abhi aapke saath discuss nahi karna chahata Because usme constantly changes Ho rahe hain, but roughly jo structure Rahega playlist ka, wo aapke screen pe Hai, thek hai, toh I hope Aapko ye curriculum pasand aara hai, jo Puri tarike se organized hai, aapko logical Lag raha hai, baaki if you have any feedbacks Aap comment me mujhe likh ke Batana, agar mujhe lagega ki aapka feedback Is really really Very solid Toh main usko integrate kar lunga Is curriculum ke andar, thek hai Ab baat kar lete hain Pre-requisites ki, ye Kaafi logon ko doubt aata hai, jab log Playlist start karte hain, ki kya hum Is playlist ko start karne ke liye ready hain ki nahi Toh is playlist ko agar aap Seekhna chahate ho, padhna chahate ho, dekhna chahate ho Toh aapko 3 cheezein aani chahiye Sabse pehli cheezein, aapko python aana chahiye Aur for a change Main ye nahi bolunga ki basic python Is playlist me aapko Thoda intermediate level python Aana chahiye, yaha pe hum kuch aasi cheezein Use karenge, jo basic python Ke andar nahi aati, will be using OOP a lot, matlab OOPS ke principle toh use hone hi hai Uske alawa Aapka typing module, pydantic Ye saari cheezein use hongi Async IO, ye bhi use hoga Toh ye saari kuch advanced concepts jo hain Python me, ye aapko aane chahiye Agar ye sab nahi aata hai aapko, toh fir Ye playlist ko bohot acche tariqe se follow Nahi kar paoge, toh this is The thing number one, the second Thing is, you should have some Familiarity with LLMs LLMs ke saath kaam karne ka thoda Idea aapko hona chahiye, toh Agar aapne mera lang chain playlist dekha hoga Toh fir ye particular cheezein aapko Problem nahi hogi, the third thing is Lang chain, lang graph Lang chain ke upar hi bana ho hai So. constantly aap is playlist me So. you will see that whenever we are writing any code Toh waha pe koi na koi dependency Lang chain ki zaroor rehegi Toh agar aapne lang chain zara bhi nahi padha hai Toh fir ye playlist aapke sir ke upar se jayegi Toh I would recommend, I will highly Recommend, ki aap ek baar mera Lang chain playlist zaroor dekho Kuch 18 videos hai us playlist me, thode lambe Lambe videos hai, but agar aap wo dekh lete ho Toh is particular playlist me aapko bohot Help ho jayegi, theke So. prerequisites point of view se Ye 3 cheezein aapko aani chahiye Ab kuch aur Important questions is point me answer karna Chahunga, pehla Ki bohot log pooch lete hain, isliye Mata raha hoon, ki total kitne number of videos Rahenge, total is playlist me Kitne videos rahenge, so. is point Pe aapko exact count nahi bata sakta But mere estimation Ke hisaab se, somewhere around 35 to 50 videos rahne wale hain Is playlist ke andar, again Since bohot tezi se Agentic AI evolve ho raha hai Lang graph me khud me bohot changes Ho rahe hain, to exact Estimate banana possible nahi hai, but ya Somewhere between 35 to 50 Itne videos rahne wale hain, theke Agla video upload Frequency kya rahegi I'll be very honest, mai Puri koshish karunga, ki mai ek Week me aapko 3 videos la karke do Theke, 3 se zyada mere liye possible Nahi hai, aur Agar kabhi mai 3 se kam banaunga Toh uske peeche kuch reason hoga Because, you know, sabki ek personal Life hoti hai, usme cheeze ho sakti hai Toh aap please usko thoda leke chalna But, mai apni side se ye Commitment aapko dena chahunga, ki I will try To upload 3 videos per Week, theke, toh baaki calculation Aap kar lo, ki what will be the timeline, kitna Time lagega, is poore playlist ko complete hone me So. ya, ye 2 questions Mujhe lagta hai, bohot log puchte hain, toh pehle se Mai answer kar deta ho, iske alawa aur koi Questions agar aapke hain, toh aap comment Puch sakte ho, mai ya Team usko answer karne ki koshish karenge Baaki, agar Aap lang graph sekhna chahate ho Toh please aap ye playlist follow karo Maine, kind of Khud se promise kiya hai, to Build, to create the best Playlist on lang graph, aur Agle 3-4 mahinon me, mai Apna 100% lagane wala hu, Us playlist ke upar, and I really hope, ki ye jo playlist banegi Future mein, aap logon ko bohot pasand Aayegi, so. super excited for the playlist I really hope aap bhi ho, agar Aapko ye video pasand aaya Aur jo hum karne wale hain, wo pasand Aaya, please is video ko aap like karo Apne friends ke saath share karo, jo bhi Lang graph padhna chahate hain, aur agar aapne Is channel ko subscribe nahi kiya hai, please Do subscribe, milte hain next video me Bye\"]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["video_text"]}, {"cell_type": "code", "execution_count": 11, "id": "440bb2c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing video: videolong.mp4\n", "Audio file size: 79.42 MB\n", "Large file detected. Using chunking strategy...\n", "Chunking audio for processing...\n", "Audio split into 87 chunks\n", "Starting transcription of chunks...\n", "Transcribing chunk 1/87...\n", "✓ Chunk 1 completed (919 characters)\n", "Transcribing chunk 2/87...\n", "✓ Chunk 2 completed (938 characters)\n", "Transcribing chunk 3/87...\n", "✓ Chunk 3 completed (964 characters)\n", "Transcribing chunk 4/87...\n", "✓ Chunk 4 completed (688 characters)\n", "Transcribing chunk 5/87...\n", "✓ Chunk 5 completed (860 characters)\n", "Transcribing chunk 6/87...\n", "✓ Chunk 6 completed (898 characters)\n", "Transcribing chunk 7/87...\n", "✓ Chunk 7 completed (901 characters)\n", "Transcribing chunk 8/87...\n", "✓ Chunk 8 completed (977 characters)\n", "Transcribing chunk 9/87...\n", "✓ Chunk 9 completed (369 characters)\n", "Transcribing chunk 10/87...\n", "✓ Chunk 10 completed (403 characters)\n", "Transcribing chunk 11/87...\n", "✓ Chunk 11 completed (598 characters)\n", "Transcribing chunk 12/87...\n", "✓ Chunk 12 completed (949 characters)\n", "Transcribing chunk 13/87...\n", "✓ Chunk 13 completed (946 characters)\n", "Transcribing chunk 14/87...\n", "✓ Chunk 14 completed (756 characters)\n", "Transcribing chunk 15/87...\n", "✓ Chunk 15 completed (556 characters)\n", "Transcribing chunk 16/87...\n", "✓ Chunk 16 completed (881 characters)\n", "Transcribing chunk 17/87...\n", "✓ Chunk 17 completed (740 characters)\n", "Transcribing chunk 18/87...\n", "✓ Chunk 18 completed (843 characters)\n", "Transcribing chunk 19/87...\n", "✓ Chunk 19 completed (862 characters)\n", "Transcribing chunk 20/87...\n", "✓ Chunk 20 completed (866 characters)\n", "Transcribing chunk 21/87...\n", "✓ Chunk 21 completed (512 characters)\n", "Transcribing chunk 22/87...\n", "✓ Chunk 22 completed (761 characters)\n", "Transcribing chunk 23/87...\n", "✓ Chunk 23 completed (834 characters)\n", "Transcribing chunk 24/87...\n", "✓ Chunk 24 completed (910 characters)\n", "Transcribing chunk 25/87...\n", "✓ Chunk 25 completed (843 characters)\n", "Transcribing chunk 26/87...\n", "✓ Chunk 26 completed (809 characters)\n", "Transcribing chunk 27/87...\n", "✓ Chunk 27 completed (715 characters)\n", "Transcribing chunk 28/87...\n", "✓ Chunk 28 completed (850 characters)\n", "Transcribing chunk 29/87...\n", "✓ Chunk 29 completed (804 characters)\n", "Transcribing chunk 30/87...\n", "✓ Chunk 30 completed (683 characters)\n", "Transcribing chunk 31/87...\n", "✓ Chunk 31 completed (896 characters)\n", "Transcribing chunk 32/87...\n", "✓ Chunk 32 completed (899 characters)\n", "Transcribing chunk 33/87...\n", "✓ Chunk 33 completed (340 characters)\n", "Transcribing chunk 34/87...\n", "✓ Chunk 34 completed (475 characters)\n", "Transcribing chunk 35/87...\n", "✓ Chunk 35 completed (947 characters)\n", "Transcribing chunk 36/87...\n", "✓ Chunk 36 completed (439 characters)\n", "Transcribing chunk 37/87...\n", "✓ Chunk 37 completed (494 characters)\n", "Transcribing chunk 38/87...\n", "✓ Chunk 38 completed (601 characters)\n", "Transcribing chunk 39/87...\n", "✓ Chunk 39 completed (957 characters)\n", "Transcribing chunk 40/87...\n", "✓ Chunk 40 completed (730 characters)\n", "Transcribing chunk 41/87...\n", "✓ Chunk 41 completed (524 characters)\n", "Transcribing chunk 42/87...\n", "✓ Chunk 42 completed (804 characters)\n", "Transcribing chunk 43/87...\n", "✓ Chunk 43 completed (403 characters)\n", "Transcribing chunk 44/87...\n", "✓ Chunk 44 completed (827 characters)\n", "Transcribing chunk 45/87...\n", "✓ Chunk 45 completed (855 characters)\n", "Transcribing chunk 46/87...\n", "✓ Chunk 46 completed (410 characters)\n", "Transcribing chunk 47/87...\n", "✓ Chunk 47 completed (260 characters)\n", "Transcribing chunk 48/87...\n", "✓ Chunk 48 completed (421 characters)\n", "Transcribing chunk 49/87...\n", "✓ Chunk 49 completed (755 characters)\n", "Transcribing chunk 50/87...\n", "✓ Chunk 50 completed (601 characters)\n", "Transcribing chunk 51/87...\n", "✓ Chunk 51 completed (771 characters)\n", "Transcribing chunk 52/87...\n", "✓ Chunk 52 completed (652 characters)\n", "Transcribing chunk 53/87...\n", "✓ Chunk 53 completed (729 characters)\n", "Transcribing chunk 54/87...\n", "✓ Chunk 54 completed (765 characters)\n", "Transcribing chunk 55/87...\n", "✓ Chunk 55 completed (654 characters)\n", "Transcribing chunk 56/87...\n", "✓ Chunk 56 completed (667 characters)\n", "Transcribing chunk 57/87...\n", "✓ Chunk 57 completed (809 characters)\n", "Transcribing chunk 58/87...\n", "✓ Chunk 58 completed (474 characters)\n", "Transcribing chunk 59/87...\n", "✓ Chunk 59 completed (521 characters)\n", "Transcribing chunk 60/87...\n", "✓ Chunk 60 completed (614 characters)\n", "Transcribing chunk 61/87...\n", "✓ Chunk 61 completed (462 characters)\n", "Transcribing chunk 62/87...\n", "✓ Chunk 62 completed (521 characters)\n", "Transcribing chunk 63/87...\n", "✓ Chunk 63 completed (633 characters)\n", "Transcribing chunk 64/87...\n", "✓ Chunk 64 completed (648 characters)\n", "Transcribing chunk 65/87...\n", "✓ Chunk 65 completed (623 characters)\n", "Transcribing chunk 66/87...\n", "✓ Chunk 66 completed (698 characters)\n", "Transcribing chunk 67/87...\n", "✓ Chunk 67 completed (716 characters)\n", "Transcribing chunk 68/87...\n", "✓ Chunk 68 completed (683 characters)\n", "Transcribing chunk 69/87...\n", "✓ Chunk 69 completed (851 characters)\n", "Transcribing chunk 70/87...\n", "✓ Chunk 70 completed (579 characters)\n", "Transcribing chunk 71/87...\n", "✓ Chunk 71 completed (661 characters)\n", "Transcribing chunk 72/87...\n", "✓ Chunk 72 completed (744 characters)\n", "Transcribing chunk 73/87...\n", "✓ Chunk 73 completed (678 characters)\n", "Transcribing chunk 74/87...\n", "✓ Chunk 74 completed (607 characters)\n", "Transcribing chunk 75/87...\n", "✓ Chunk 75 completed (656 characters)\n", "Transcribing chunk 76/87...\n", "✓ Chunk 76 completed (454 characters)\n", "Transcribing chunk 77/87...\n", "✓ Chunk 77 completed (524 characters)\n", "Transcribing chunk 78/87...\n", "✓ Chunk 78 completed (883 characters)\n", "Transcribing chunk 79/87...\n", "✓ Chunk 79 completed (473 characters)\n", "Transcribing chunk 80/87...\n", "✓ Chunk 80 completed (554 characters)\n", "Transcribing chunk 81/87...\n", "✓ Chunk 81 completed (702 characters)\n", "Transcribing chunk 82/87...\n", "✓ Chunk 82 completed (768 characters)\n", "Transcribing chunk 83/87...\n", "✓ Chunk 83 completed (696 characters)\n", "Transcribing chunk 84/87...\n", "✓ Chunk 84 completed (688 characters)\n", "Transcribing chunk 85/87...\n", "✓ Chunk 85 completed (404 characters)\n", "Transcribing chunk 86/87...\n", "✓ Chunk 86 completed (403 characters)\n", "Transcribing chunk 87/87...\n", "✓ Chunk 87 completed (521 characters)\n", "Merging and cleaning transcripts...\n", "✓ Video processing completed! Final transcript: 59900 characters\n", "Temporary audio file cleaned up\n", "Temporary chunk files cleaned up\n", "Transcript saved to: transcript_output.txt\n", "\n", "==================================================\n", "TRANSCRIPT PREVIEW:\n", "==================================================\n", "Hi guys, my name is <PERSON><PERSON><PERSON> and you are welcome to my YouTube channel इस वीडियो में हम लोग एक ऐसे टॉपिक के बारे में बात करने जा रहे हैं जिसको आप सौफ्ट्वेर की दुनिया का Universal टॉपिक बुला सकते हो Universal का मतलब कि आप सौफ्वेर में किसी भी प्रोफाईल में काम कर रहे हो आपको इस पर्टिकुलर टॉपिक की जरूरत जरूर पड़ेगी भले या आप सौफ्वेर डेवलपर हो या वेब डेवलपर हो या फिर आप डेटा साइन्टिस्ट हो या MLOPS इंजीनियर हो या फिर आप DevOps में काम करते हो इन सारे प्रोफाईल में आपको आज के टॉपिक की जरूरत पड़ेगी और आज ...\n"]}], "source": ["video_file = \"videolong.mp4\"  # Replace with your video path\n", "    \n", "# Extract text from video\n", "transcript = process_video_file(video_file, \"transcript_output.txt\")\n", "    \n", "# Print first 500 characters as preview\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"TRANSCRIPT PREVIEW:\")\n", "print(\"=\"*50)\n", "print(transcript[:500] + \"...\" if len(transcript) > 500 else transcript)"]}, {"cell_type": "code", "execution_count": null, "id": "bafd030b", "metadata": {}, "outputs": [], "source": ["import os\n", "import tempfile\n", "import shutil\n", "import subprocess  # For FFmpeg\n", "import openai\n", "from pydub import AudioSegment\n", "from pydub.utils import make_chunks\n", "from dotenv import load_dotenv\n", "\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "os.environ['OPENAI_API_KEY'] = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "\n", "import subprocess\n", "\n", "def extract_audio_from_video(video_path: str, output_audio_path: str) -> str:\n", "    try:\n", "        if os.path.exists(output_audio_path):\n", "            os.remove(output_audio_path)\n", "\n", "        command = [\n", "            \"ffmpeg\",\n", "            \"-i\", video_path,\n", "            \"-vn\",                     # no video\n", "            \"-acodec\", \"libmp3lame\",   # mp3 format\n", "            \"-y\",                      # overwrite output\n", "            output_audio_path\n", "        ]\n", "\n", "        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)\n", "\n", "        if result.returncode != 0:\n", "            raise RuntimeError(f\"FFmpeg error: {result.stderr}\")\n", "\n", "        return output_audio_path\n", "\n", "    except Exception as e:\n", "        raise Exception(f\"Error extracting audio using FFmpeg: {str(e)}\")\n", "\n", "\n", "\n", "def chunk_audio(audio_path: str, chunk_length_ms: int = 60000) -> tuple:\n", "    \"\"\"Split audio into chunks for processing (default: 60 seconds per chunk)\"\"\"\n", "    try:\n", "        print(\"Chunking audio for processing...\")\n", "        audio = AudioSegment.from_file(audio_path)\n", "        chunks = make_chunks(audio, chunk_length_ms)\n", "        \n", "        chunk_paths = []\n", "        temp_dir = tempfile.mkdtemp()\n", "        \n", "        for i, chunk in enumerate(chunks):\n", "            chunk_path = os.path.join(temp_dir, f\"chunk_{i:03d}.mp3\")\n", "            chunk.export(chunk_path, format=\"mp3\")\n", "            chunk_paths.append(chunk_path)\n", "            \n", "        print(f\"Audio split into {len(chunk_paths)} chunks\")\n", "        return chunk_paths, temp_dir\n", "    except Exception as e:\n", "        raise Exception(f\"Error chunking audio: {str(e)}\")\n", "\n", "\n", "def transcribe_audio_chunk(chunk_path: str) -> str:\n", "    \"\"\"Transcribe a single audio chunk using OpenAI Whisper\"\"\"\n", "    try:\n", "        with open(chunk_path, \"rb\") as audio_file:\n", "            transcript = openai.audio.transcriptions.create(\n", "                model=\"whisper-1\",\n", "                file=audio_file,\n", "                response_format=\"text\"\n", "            )\n", "        return transcript.strip()\n", "    except Exception as e:\n", "        print(f\"Error transcribing chunk {chunk_path}: {str(e)}\")\n", "        return \"\"\n", "\n", "\n", "def clean_and_merge_transcripts(transcripts: list) -> str:\n", "    \"\"\"Clean and merge all transcripts into a coherent text\"\"\"\n", "    # Remove empty transcripts\n", "    valid_transcripts = [t for t in transcripts if t.strip()]\n", "    \n", "    if not valid_transcripts:\n", "        return \"No valid transcripts found.\"\n", "    \n", "    # Join transcripts with space\n", "    combined_transcript = \" \".join(valid_transcripts)\n", "    \n", "    # Clean the transcript\n", "    import re\n", "    \n", "    # Remove extra whitespace and normalize\n", "    cleaned = \" \".join(combined_transcript.split())\n", "    \n", "    # Add basic sentence structure improvements\n", "    # Add periods after common sentence-ending words if missing\n", "    cleaned = re.sub(r'\\b(thank you|thanks|okay|alright|right|yes|no|well|so)\\b(?!\\.)(?=\\s+[A-Z])', r'\\1.', cleaned, flags=re.IGNORECASE)\n", "    \n", "    # Ensure proper spacing after periods\n", "    cleaned = re.sub(r'\\.(?=[A-Za-z])', '. ', cleaned)\n", "    \n", "    # Remove multiple spaces\n", "    cleaned = re.sub(r'\\s+', ' ', cleaned)\n", "    \n", "    # Capitalize first letter\n", "    if cleaned:\n", "        cleaned = cleaned[0].upper() + cleaned[1:] if len(cleaned) > 1 else cleaned.upper()\n", "    \n", "    return cleaned.strip()\n", "\n", "\n", "def extract_text_from_video(video_path: str) -> str:\n", "    \"\"\"\n", "    Main function to extract text from video file\n", "    Process: Video → Audio → Chunks → Transcription → Merge → Clean\n", "    \"\"\"\n", "    temp_audio_path = None\n", "    temp_dir = None\n", "    \n", "    try:\n", "        # Step 1: Validate video file\n", "        if not os.path.exists(video_path):\n", "            raise Exception(f\"Video file not found: {video_path}\")\n", "        \n", "        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']\n", "        if not any(video_path.lower().endswith(ext) for ext in video_extensions):\n", "            raise Exception(f\"Unsupported video format. Supported formats: {', '.join(video_extensions)}\")\n", "        \n", "        print(f\"Processing video: {video_path}\")\n", "        \n", "        # Step 2: Extract audio from video\n", "        temp_audio_path = tempfile.mktemp(suffix=\".mp3\")\n", "        extract_audio_from_video(video_path, temp_audio_path)\n", "        \n", "        # Step 3: Check file size and decide chunking strategy\n", "        file_size_mb = os.path.getsize(temp_audio_path) / (1024 * 1024)\n", "        print(f\"Audio file size: {file_size_mb:.2f} MB\")\n", "        \n", "        transcripts = []\n", "        \n", "        if file_size_mb > 20:  # OpenAI has 25MB limit, using 20MB as safe buffer\n", "            print(\"Large file detected. Using chunking strategy...\")\n", "            \n", "            # Step 4: Chunk the audio\n", "            chunk_paths, temp_dir = chunk_audio(temp_audio_path, chunk_length_ms=60000)  # 60 seconds\n", "            \n", "            # Step 5: Transcribe each chunk\n", "            print(\"Starting transcription of chunks...\")\n", "            for i, chunk_path in enumerate(chunk_paths, 1):\n", "                print(f\"Transcribing chunk {i}/{len(chunk_paths)}...\")\n", "                transcript = transcribe_audio_chunk(chunk_path)\n", "                if transcript:\n", "                    transcripts.append(transcript)\n", "                    print(f\"✓ Chunk {i} completed ({len(transcript)} characters)\")\n", "                else:\n", "                    print(f\"✗ Chunk {i} failed or empty\")\n", "            \n", "        else:\n", "            print(\"Small file detected. Direct transcription...\")\n", "            # Step 5: Direct transcription for smaller files\n", "            transcript = transcribe_audio_chunk(temp_audio_path)\n", "            if transcript:\n", "                transcripts.append(transcript)\n", "        \n", "        # Step 6: Merge and clean transcripts\n", "        print(\"Merging and cleaning transcripts...\")\n", "        final_transcript = clean_and_merge_transcripts(transcripts)\n", "        \n", "        print(f\"✓ Video processing completed! Final transcript: {len(final_transcript)} characters\")\n", "        return final_transcript\n", "        \n", "    except Exception as e:\n", "        error_msg = f\"Error processing video {video_path}: {str(e)}\"\n", "        print(error_msg)\n", "        return error_msg\n", "    \n", "    finally:\n", "        # Cleanup temporary files\n", "        if temp_audio_path and os.path.exists(temp_audio_path):\n", "            os.remove(temp_audio_path)\n", "            print(\"Temporary audio file cleaned up\")\n", "        \n", "        if temp_dir and os.path.exists(temp_dir):\n", "            shutil.rmtree(temp_dir)\n", "            print(\"Temporary chunk files cleaned up\")\n", "\n", "\n", "def process_video_file(video_path: str, output_file: str = None) -> str:\n", "    \"\"\"\n", "    Process a video file and optionally save transcript to file\n", "    \n", "    Args:\n", "        video_path: Path to the video file\n", "        output_file: Optional path to save the transcript\n", "    \n", "    Returns:\n", "        Extracted transcript text\n", "    \"\"\"\n", "    transcript = extract_text_from_video(video_path)\n", "    \n", "    if output_file:\n", "        try:\n", "            with open(output_file, 'w', encoding='utf-8') as f:\n", "                f.write(f\"VIDEO TRANSCRIPT\\n\")\n", "                f.write(\"=\" * 50 + \"\\n\")\n", "                f.write(f\"Source: {video_path}\\n\")\n", "                f.write(f\"Transcript Length: {len(transcript)} characters\\n\")\n", "                f.write(\"=\" * 50 + \"\\n\\n\")\n", "                f.write(transcript)\n", "            print(f\"Transcript saved to: {output_file}\")\n", "        except Exception as e:\n", "            print(f\"Error saving transcript: {str(e)}\")\n", "    \n", "    return transcript\n", "\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    # Example: Process a single video file\n", "    video_file = \"path/to/your/video.mp4\"  # Replace with your video path\n", "    \n", "    # Extract text from video\n", "    transcript = process_video_file(video_file, \"transcript_output.txt\")\n", "    \n", "    # Print first 500 characters as preview\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"TRANSCRIPT PREVIEW:\")\n", "    print(\"=\"*50)\n", "    print(transcript[:500] + \"...\" if len(transcript) > 500 else transcript)"]}, {"cell_type": "code", "execution_count": 1, "id": "a2589915", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["1%1"]}, {"cell_type": "code", "execution_count": null, "id": "9f0d65c8", "metadata": {}, "outputs": [], "source": ["def analyze_input_complexity(extracted_text):\n", "    \"\"\"Analyze input complexity using multiple intelligent indicators\"\"\"\n", "    \n", "    # Basic metrics\n", "    word_count = len(extracted_text.split())\n", "    sentence_count = len([s for s in extracted_text.split('.') if s.strip()])\n", "    unique_words = len(set(extracted_text.lower().split()))\n", "    \n", "    # Structural complexity indicators\n", "    structural_indicators = {\n", "        'lists_or_bullets': len([line for line in extracted_text.split('\\n') if line.strip().startswith(('-', '*', '•', '1.', '2.'))]),\n", "        'questions': extracted_text.count('?'),\n", "        'specifications': len([word for word in extracted_text.split() if word.lower() in ['specific', 'exactly', 'precisely', 'detailed']]),\n", "        'conditional_statements': len([word for word in extracted_text.split() if word.lower() in ['if', 'when', 'unless', 'provided', 'depending']]),\n", "        'quantitative_data': len([word for word in extracted_text.split() if any(char.isdigit() for char in word)]),\n", "    }\n", "    \n", "    # Domain complexity (let the LLM identify domains instead of static keywords)\n", "    domain_complexity = {\n", "        'technical_depth': count_technical_concepts(extracted_text),\n", "        'business_depth': count_business_concepts(extracted_text), \n", "        'requirement_specificity': count_requirement_specificity(extracted_text),\n", "        'process_complexity': count_process_indicators(extracted_text)\n", "    }\n", "    \n", "    # Calculate composite complexity score\n", "    structural_score = sum(structural_indicators.values())\n", "    domain_score = sum(domain_complexity.values())\n", "    vocabulary_richness = unique_words / max(word_count, 1)  # Avoid division by zero\n", "    \n", "    # Determine detail level using composite scoring\n", "    total_complexity = (structural_score * 0.3) + (domain_score * 0.4) + (word_count * 0.002) + (vocabulary_richness * 100 * 0.3)\n", "    \n", "    if total_complexity < 10:\n", "        return \"minimal\", \"1-2 sentences per section, focus on essential information only\"\n", "    elif total_complexity < 25:\n", "        return \"brief\", \"2-3 sentences per section, cover key points concisely\"  \n", "    elif total_complexity < 50:\n", "        return \"moderate\", \"1-2 paragraphs per section with adequate detail\"\n", "    else:\n", "        return \"detailed\", \"Multiple paragraphs per section with comprehensive coverage\"\n", "\n", "def count_technical_concepts(text):\n", "    \"\"\"Count technical concept indicators without static keywords\"\"\"\n", "    technical_patterns = [\n", "        r'\\b\\w+\\s+(protocol|framework|library|service|system|platform|technology)\\b',\n", "        r'\\b(implementation|deployment|configuration|optimization|scalability)\\b',\n", "        r'\\b\\w+\\.(com|org|net|io)\\b',  # URLs/domains\n", "        r'\\b[A-Z]{2,}\\b',  # Acronyms (often technical)\n", "        r'\\b\\d+\\s*(GB|MB|KB|TB|Hz|GHz|MHz)\\b',  # Technical units\n", "        r'\\b(v\\d+\\.\\d+|version\\s+\\d+)\\b',  # Version numbers\n", "    ]\n", "    import re\n", "    return sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in technical_patterns)\n", "\n", "def count_business_concepts(text):\n", "    \"\"\"Count business concept indicators dynamically\"\"\"\n", "    business_patterns = [\n", "        r'\\$\\d+',  # Dollar amounts\n", "        r'\\b\\d+%\\b',  # Percentages\n", "        r'\\b(quarter|Q[1-4]|fiscal|annual|monthly)\\b',  # Time periods\n", "        r'\\b(increase|decrease|growth|profit|loss|ROI|KPI)\\b',  # Business metrics\n", "        r'\\b(customers?|clients?|users?|audience|market)\\b',  # Stakeholder terms\n", "        r'\\b(strategy|objective|goal|target|milestone)\\b',  # Strategic terms\n", "    ]\n", "    import re\n", "    return sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in business_patterns)\n", "\n", "def count_requirement_specificity(text):\n", "    \"\"\"Count how specific/detailed the requirements are\"\"\"\n", "    specificity_patterns = [\n", "        r'\\b(must|shall|should|will|needs?\\s+to|required?\\s+to)\\b',  # Requirement language\n", "        r'\\b(exactly|specifically|precisely|detailed|comprehensive)\\b',  # Precision indicators\n", "        r'\\b(step\\s+\\d+|first|second|then|next|finally)\\b',  # Process steps\n", "        r'\\b(when|if|unless|provided|given|assuming)\\b',  # Conditional logic\n", "    ]\n", "    import re\n", "    return sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in specificity_patterns)\n", "\n", "def count_process_indicators(text):\n", "    \"\"\"Count process and workflow complexity\"\"\"\n", "    process_patterns = [\n", "        r'\\b(workflow|process|procedure|methodology|approach)\\b',\n", "        r'\\b(integrate|connect|sync|communicate|interact)\\b',\n", "        r'\\b(before|after|during|while|simultaneously)\\b',\n", "        r'\\b(depends?\\s+on|requires?|needs?|relies?\\s+on)\\b',\n", "    ]\n", "    import re\n", "    return sum(len(re.findall(pattern, text, re.IGNORECASE)) for pattern in process_patterns)\n", "\n", "def generate_enhanced_prompt(extracted_text, persona, user_instruction):\n", "    \"\"\"Generate enhanced prompt with LLM-based complexity analysis\"\"\"\n", "    \n", "    enhanced_template = f\"\"\"\n", "{persona}\n", "\n", "STEP 1 - COMPLEXITY ANALYSIS:\n", "Before generating the PRD, analyze the following input and determine:\n", "\n", "Input Text: \"{extracted_text}\"\n", "User Instructions: \"{user_instruction}\"\n", "\n", "Analyze and determine:\n", "1. **Information Richness**: How much detailed information is provided? (Sparse/Moderate/Rich)\n", "2. **Technical Complexity**: How technically complex is the project? (Simple/Moderate/Complex)\n", "3. **Business Scope**: How broad is the business scope? (Narrow/Medium/Broad)\n", "4. **Requirement Specificity**: How specific are the requirements? (Vague/Moderate/Detailed)\n", "\n", "Based on this analysis, choose the appropriate detail level:\n", "- **MINIMAL**: Very basic input with limited information\n", "- **BRIEF**: Some information provided but not comprehensive  \n", "- **MODERATE**: Good amount of information with reasonable complexity\n", "- **DETAILED**: Rich, complex input requiring comprehensive coverage\n", "\n", "STEP 2 - GENERATE PRD:\n", "Using the complexity level determined above, generate a PRD with appropriate section depth.\n", "\n", "ADAPTIVE SECTION GUIDELINES:\n", "- **MINIMAL Level**: 1-2 sentences per section, focus only on what's explicitly provided\n", "- **BRIEF Level**: 2-4 sentences per section, reasonable inference from context\n", "- **MODERATE Level**: 1-2 paragraphs per section with logical extrapolation\n", "- **DETAILED Level**: Multiple paragraphs with comprehensive analysis and recommendations\n", "\n", "The PRD should include the following sections:\n", "\n", "1. **Project Title** - Clear, concise title reflecting the project scope\n", "\n", "2. **Project Overview** - Adjust depth based on complexity analysis\n", "\n", "3. **Business Objectives** - Scale based on business context available\n", "\n", "4. **Functional Requirements** - Match detail to requirement specificity identified\n", "\n", "5. **Non-Functional Requirements** - Include based on technical complexity level\n", "\n", "6. **Stakeholders** - Detail level based on scope analysis\n", "\n", "7. **Timeline and Milestones** - Adjust based on information richness\n", "\n", "8. **Assumptions and Constraints** - Scale with complexity level\n", "\n", "9. **Risks and Mitigation** - Match to technical and business complexity\n", "\n", "10. **Conclusion or Recommendations** - Depth matches overall analysis\n", "\n", "QUALITY RULES:\n", "✓ First state your complexity analysis decision\n", "✓ Scale ALL sections consistently with that decision  \n", "✓ Don't fabricate information beyond reasonable inference\n", "✓ Mark uncertain items as \"To be determined\"\n", "✓ Focus on value over word count\n", "✓ Maintain professional tone regardless of detail level\n", "\"\"\"\n", "    \n", "    return enhanced_template\n", "\n", "# Example usage\n", "extracted_text = \"Build a mobile app for food delivery\"\n", "persona = \"You are a senior product manager with 10+ years of experience\"\n", "user_instruction = \"Focus on MVP features\"\n", "\n", "enhanced_prompt = generate_enhanced_prompt(extracted_text, persona, user_instruction)\n", "print(\"Generated Prompt:\")\n", "print(\"=\"*50)\n", "print(enhanced_prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "c07ce8ff", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import load_prompt, ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_core.messages import HumanMessage, AIMessage\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_core.prompts import PromptTemplate\n", "from dotenv import load_dotenv\n", "from data_extractor import extract_all_texts_from_folder\n", "import os\n", "import re\n", "import psycopg2\n", "from datetime import datetime, timezone\n", "import uuid\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "os.environ['OPENAI_API_KEY'] = os.getenv(\"OPENAI_API_KEY\")\n", "openai_api_key = os.getenv(\"OPENAI_API_KEY\")\n", "DATABASE_URL = os.getenv(\"DATABASE_URL\") or \"******************************************************/prd_db\"\n", "\n", "\n", "# ========== INTELLIGENT PRD PROMPT TEMPLATE ==========\n", "def create_intelligent_prd_prompt(extracted_text, persona, user_instruction):\n", "    \"\"\"\n", "    Generate intelligent PRD prompt with LLM-based complexity analysis\n", "    This replaces your static template.json approach\n", "    \"\"\"\n", "    return f\"\"\"\n", "{persona}\n", "\n", "=== INTELLIGENT COMPLEXITY ANALYSIS ===\n", "\n", "Before generating the PRD, you must first analyze the input complexity using your understanding of context, not keyword matching.\n", "\n", "INPUT TO ANALYZE:\n", "Extracted Text: \"{extracted_text}\"\n", "User Instructions: \"{user_instruction}\"\n", "\n", "ANALYSIS FRAMEWORK:\n", "Carefully evaluate these four dimensions:\n", "\n", "1. **Information Richness**: \n", "   - SPARSE: Minimal details, basic concept only\n", "   - MODERATE: Some details provided, reasonable context\n", "   - RICH: Comprehensive information with multiple aspects covered\n", "\n", "2. **Technical Complexity**:\n", "   - SIMPLE: Basic functionality, straightforward implementation\n", "   - MODERATE: Some technical challenges, integration needed\n", "   - COMPLEX: Advanced technical requirements, multiple systems/technologies\n", "\n", "3. **Business Scope**:\n", "   - NARROW: Single function/department, limited stakeholders\n", "   - MEDIUM: Multiple functions, several stakeholder groups\n", "   - BROAD: Enterprise-wide, complex stakeholder ecosystem\n", "\n", "4. **Requirement Specificity**:\n", "   - VAGUE: General ideas, unclear requirements\n", "   - MODERATE: Some specific requirements, reasonable clarity\n", "   - DETAILED: Precise requirements, clear acceptance criteria\n", "\n", "COMPLEXITY LEVEL DETERMINATION:\n", "Based on your analysis above, determine the overall complexity level:\n", "\n", "- **MINIMAL**: Mostly sparse/simple/narrow/vague characteristics\n", "- **BRIEF**: Mix of sparse-moderate characteristics\n", "- **MODERATE**: Mostly moderate characteristics across dimensions\n", "- **DETAILED**: Rich/complex/broad/detailed characteristics dominate\n", "\n", "=== PRD GENERATION INSTRUCTIONS ===\n", "\n", "STEP 1: State your complexity analysis decision clearly\n", "Example: \"COMPLEXITY ANALYSIS: This project shows MODERATE complexity due to [specific reasons]\"\n", "\n", "STEP 2: Generate PRD sections with appropriate depth based on your analysis\n", "\n", "ADAPTIVE DEPTH GUIDELINES:\n", "• **MINIMAL**: 1-2 sentences per section, stick to explicit information only\n", "• **BRIEF**: 2-4 sentences per section, minimal reasonable inference\n", "• **MODERATE**: 1-2 paragraphs per section, logical extrapolation allowed\n", "• **DETAILED**: Multiple paragraphs, comprehensive analysis and strategic recommendations\n", "\n", "SECTION STRUCTURE:\n", "Generate the following sections with depth matching your complexity analysis:\n", "\n", "1. **Project Title**\n", "   - Create clear, descriptive title reflecting actual scope\n", "\n", "2. **Project Overview** \n", "   - Summarize purpose, scope, and key context\n", "   - Depth should match information richness identified\n", "\n", "3. **Business Objectives**\n", "   - Scale based on business scope analysis\n", "   - Include strategic context if complexity warrants it\n", "\n", "4. **Functional Requirements**\n", "   - Detail level must match requirement specificity identified\n", "   - Group related requirements logically\n", "\n", "5. **Non-Functional Requirements**\n", "   - Include based on technical complexity level\n", "   - Address performance, security, scalability as relevant\n", "\n", "6. **Stakeholders**\n", "   - Detail based on business scope analysis\n", "   - Include roles, responsibilities, influence levels as appropriate\n", "\n", "7. **Timeline and Milestones**\n", "   - Provide timeline estimates matching project complexity\n", "   - Break down phases according to scope\n", "\n", "8. **Assumptions and Constraints**\n", "   - Scale with overall complexity level\n", "   - Address technical, business, and resource constraints\n", "\n", "9. **Risks and Mitigation**\n", "   - Risk assessment depth should match complexity analysis\n", "   - Include technical, business, and operational risks\n", "\n", "10. **Conclusion and Next Steps**\n", "    - Summarize key points proportional to project scope\n", "    - Provide actionable recommendations\n", "\n", "=== QUALITY STANDARDS ===\n", "\n", "✓ **Transparency**: Always show your complexity analysis reasoning\n", "✓ **Consistency**: All sections must scale together with your analysis\n", "✓ **Accuracy**: Don't fabricate details beyond reasonable professional inference\n", "✓ **Clarity**: <PERSON> uncertain information as \"To be determined based on further analysis\"\n", "✓ **Value Focus**: Prioritize meaningful content over word count\n", "✓ **Professional Tone**: Maintain quality regardless of detail level\n", "✓ **Logical Flow**: Ensure sections connect and support each other\n", "\n", "Now proceed with your analysis and PRD generation.\n", "\"\"\"\n", "\n", "\n", "# ========== OTHER PROMPT TEMPLATES ==========\n", "persona_prompt = PromptTemplate(\n", "    input_variables=[\"extracted_text\"],\n", "    template=\"\"\"\n", "You are an expert in business analysis and technical documentation.\n", "\n", "Your task is to identify the most relevant professional role or persona the AI assistant should adopt to best generate a Project Requirement Document (PRD), based on the provided extracted project information.\n", "\n", "Your response must follow this exact format:\n", "\"You are a [specific persona] with [X] years of experience\"\n", "\n", "Examples:\n", "- \"You are a Healthcare IT Consultant with 10 years of experience\"\n", "- \"You are an E-commerce AI Product Analyst with 15 years of experience\"\n", "- \"You are a Logistics Optimization Specialist with 12 years of experience\"\n", "- \"You are a Customer Experience AI Architect with 10 years of experience\"\n", "\n", "Persona should be:\n", "- Specific to the industry or domain mentioned\n", "- Reflective of the type of problem being solved\n", "- Focused on the role needed to analyze and convert the input into a PRD\n", "\n", "Extracted Project Information:\n", "{extracted_text}\n", "\"\"\"\n", ")\n", "\n", "# Updated intent classification prompt\n", "intent_prompt = PromptTemplate.from_template(\"\"\"\n", "You are an intent classifier for a Project Requirement Document (PRD) assistant.\n", "\n", "Your job is to classify the user's query into one of the following intents:\n", "\n", "- greeting → If the user is saying hello, hi, good morning, how are you, etc.\n", "- generate_prd → If the user wants to create or regenerate a PRD from extracted content.\n", "- query_extracted_text → If the user is asking questions about specific information in the uploaded documents (e.g., requirements, features, users, timeline, summarization,key points, important information).\n", "- unknown → If the intent is unclear, irrelevant, or doesn't fit any category.\n", "\n", "Return only the intent name: greeting, generate_prd, query_extracted_text, or unknown.\n", "\n", "Query: {query}\n", "Intent:\n", "\"\"\")\n", "\n", "\n", "# ========== INITIALIZE CHAINS ==========\n", "llm = ChatOpenAI(model=\"gpt-4o\")\n", "persona_chain = persona_prompt | llm | StrOutputParser()\n", "intent_chain = intent_prompt | llm | StrOutputParser()\n", "\n", "# Extract text from uploaded files\n", "text = extract_all_texts_from_folder(\"./database\")\n", "print(\"text\",text)\n", "\n", "persona = persona_chain.invoke({\"extracted_text\": text})\n", "print(\"Persona:\",persona)\n", "\n", "# Initialize chat history and session\n", "session_id = \"session_001\"\n", "chat_history = []\n", "chat_id = str(uuid.uuid4())\n", "now = datetime.now(timezone.utc)\n", "\n", "# Connect to PostgreSQL and load or create session\n", "try:\n", "    conn = psycopg2.connect(DATABASE_URL)\n", "    cursor = conn.cursor()\n", "\n", "    # Ensure session exists in chat_sessions\n", "    cursor.execute(\"SELECT 1 FROM chat_sessions WHERE session_id = %s\", (session_id,))\n", "    if not cursor.fetchone():\n", "        cursor.execute(\n", "            \"INSERT INTO chat_sessions (session_id, created_at, updated_at) VALUES (%s, %s, %s)\",\n", "            (session_id, now, now)\n", "        )\n", "        conn.commit()\n", "\n", "    # Load chat history from DB\n", "    cursor.execute(\"\"\"\n", "        SELECT role, message FROM chat_history\n", "        WHERE session_id = %s\n", "        ORDER BY created_at ASC;\n", "    \"\"\", (session_id,))\n", "    for role, message in cursor.fetchall():\n", "        if role == \"human\":\n", "            chat_history.append(HumanMessage(content=message))\n", "        elif role == \"ai\":\n", "            chat_history.append(AIMessage(content=message))\n", "\n", "except Exception as e:\n", "    print(\"Error during DB session load or insert:\", e)\n", "    conn.rollback()\n", "\n", "# Define ChatPromptTemplate with memory\n", "chat_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are an AI assistant for writing PRDs from extracted documents.\"),\n", "    MessagesPlaceholder(variable_name=\"chat_history\"),\n", "    (\"human\", \"{input}\")\n", "])\n", "\n", "# ========== UPDATED: USE INTELLIGENT PRD INSTEAD OF TEMPLATE.JSON ==========\n", "# REMOVE THIS LINE: prompt_template = load_prompt(\"template.json\")\n", "# CREATE INTELLIGENT PRD CHAIN INSTEAD:\n", "def create_intelligent_prd_chain():\n", "    \"\"\"Create the intelligent PRD generation chain\"\"\"\n", "    return chat_template | llm | StrOutputParser()\n", "\n", "chain = create_intelligent_prd_chain()\n", "\n", "from functools import lru_cache\n", "\n", "@lru_cache(maxsize=20)\n", "def get_cached_greeting(user_input: str) -> str:\n", "    greeting_prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", \"You are a helpful and friendly AI assistant for business professionals. You assist with project documentation and PRDs.\"),\n", "        (\"human\", \"Hi\"),\n", "        (\"ai\", \"Hello! Ready to help you with your project documentation. What can I assist you with today?\"),\n", "        (\"human\", \"Good morning\"),\n", "        (\"ai\", \"Good morning! Let me know how I can help with your PRD or project tasks.\"),\n", "        (\"human\", \"Hey assistant!\"),\n", "        (\"ai\", \"Hey there! Excited to support you on your project today.\"),\n", "        (\"human\", \"{user_input}\")\n", "    ])\n", "\n", "    dynamic_chain = greeting_prompt | llm | StrOutputParser()\n", "    return dynamic_chain.invoke({\"user_input\": user_input})\n", "\n", "# Run prompt and get result\n", "user_instruction=input(\"Enter additional instructions (or press Enter to skip): \").strip()\n", "if not user_instruction:\n", "    user_instruction = \"None\"\n", "\n", "intent = intent_chain.invoke({\"query\": user_instruction})\n", "print(\"Detected Intent:\", intent)    \n", "\n", "# ========== UPDATED: MAIN LOGIC WITH INTELLIGENT PRD ==========\n", "if intent == \"generate_prd\":\n", "    # CREATE INTELLIGENT PRD PROMPT INSTEAD OF USING TEMPLATE.JSON\n", "    intelligent_prd_input = create_intelligent_prd_prompt(\n", "        extracted_text=text,\n", "        persona=persona,\n", "        user_instruction=user_instruction\n", "    )\n", "    \n", "    # Use the intelligent prompt with your existing chain\n", "    result = chain.invoke({\n", "        \"chat_history\": chat_history, \n", "        \"input\": intelligent_prd_input\n", "    })\n", "    \n", "elif intent == \"query_extracted_text\":\n", "    result = llm.invoke([HumanMessage(content=f\"Based on the following extracted document:\\n\\n{text}\\n\\nAnswer the user's question:\\n{user_instruction}\")]).content    \n", "elif intent == \"greeting\":\n", "    result = get_cached_greeting(user_instruction)\n", "elif intent == \"unknown\":\n", "    result = \"I'm not sure how to help with that. Could you please clarify your request regarding the project document?\"\n", "\n", "print(\"\\n--- AI Response ---\\n\")\n", "print(result)\n", "\n", "# Save human + AI messages into chat_history table\n", "try:\n", "        cursor.execute(\"\"\"\n", "            INSERT INTO chat_history (session_id, role, message, chat_id, created_at)\n", "            VALUES \n", "            (%s, %s, %s, %s, %s),\n", "            (%s, %s, %s, %s, %s)\n", "        \"\"\", (\n", "            session_id, \"human\", user_instruction, chat_id, now,\n", "            session_id, \"ai\", result, chat_id, now\n", "        ))\n", "        conn.commit()\n", "except Exception as e:\n", "        print(\"Error saving chat to <PERSON>:\", e)\n", "        conn.rollback()\n", "finally:\n", "        cursor.close()\n", "        conn.close()\n", "\n", "# Normalize output headings\n", "def normalize_headings(text: str) -> str:\n", "    lines = text.splitlines()\n", "    normalized = []\n", "\n", "    for line in lines:\n", "        line = line.strip()\n", "\n", "        if re.match(r\"^#+\\s*\\d+\\..+\", line):\n", "            clean = re.sub(r\"^#+\\s*\", \"\", line).strip()\n", "            normalized.append(f\"**{clean}**\")\n", "\n", "        elif re.match(r\"^#+\\s*[A-Za-z]\", line):\n", "            clean = re.sub(r\"^#+\\s*\", \"\", line).strip()\n", "            normalized.append(f\"**{clean}**\")\n", "\n", "        elif re.match(r\"\\*\\*\\d+\\..+\\*\\*\", line):\n", "            clean = re.sub(r\"^\\*{2}|\\*{2}$\", \"\", line).strip()\n", "            normalized.append(f\"**{clean}**\")\n", "\n", "        elif re.match(r\"\\*\\*[A-Za-z\\s\\-()]+[:]*\\*\\*\", line):\n", "            clean = re.sub(r\"^\\*{2}|\\*{2}$\", \"\", line).strip()\n", "            normalized.append(f\"**{clean}**\")\n", "\n", "        else:\n", "            normalized.append(line)\n", "\n", "    return \"\\n\".join(normalized)\n", "\n", "# Print final PRD result\n", "print(normalize_headings(result))"]}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}