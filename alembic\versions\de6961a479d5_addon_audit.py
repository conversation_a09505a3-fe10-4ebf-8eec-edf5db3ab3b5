"""addon audit

Revision ID: de6961a479d5
Revises: 7ed11fcd5e2d
Create Date: 2025-06-23 17:29:07.242290

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'de6961a479d5'
down_revision: Union[str, None] = '7ed11fcd5e2d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('audit_logs', sa.Column('status_code', sa.Integer(), nullable=True))
    op.add_column('audit_logs', sa.Column('user_agent', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('audit_logs', 'user_agent')
    op.drop_column('audit_logs', 'status_code')
    # ### end Alembic commands ###
