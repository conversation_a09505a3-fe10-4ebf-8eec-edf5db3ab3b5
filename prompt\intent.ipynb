{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d4cf33b0", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "# Create the intent classifier prompt\n", "intent_prompt = PromptTemplate(\n", "    input_variables=[\"query\"],\n", "    template=\"\"\"\n", "You are an intent classifier for a Project Requirement Document (PRD) assistant.\n", "\n", "Your job is to classify the user's query into one of the following intents:\n", "\n", "- greeting → If the user is saying hello, hi, good morning, how are you, etc.\n", "- generate_prd → If the user wants to create or regenerate a PRD from extracted content for the first time.\n", "- modify_prd → If the user wants to modify, update, change, revise, edit, or enhance an existing PRD.\n", "- query_extracted_text → If the user is asking questions about specific information in the uploaded documents.\n", "- unknown → If the intent is unclear, irrelevant, or doesn't fit any category.\n", "\n", "=== MODIFICATION INTENT HINTS ===\n", "The query is likely `modify_prd` if it includes:\n", "• Words like: modify, change, update, revise, edit, enhance, improve, add to, remove from, adjust  \n", "• References to specific PRD sections: \"update timeline\", \"change requirements\", \"modify stakeholders\"  \n", "• Iterative phrasing: \"also add\", \"now include\", \"further enhance\", \"make it more concise\"\n", "\n", "=== OUTPUT INSTRUCTION ===\n", "Return only the intent name as one of:\n", "`greeting`, `generate_prd`, `modify_prd`, `query_extracted_text`, or `unknown`\n", "\n", "=== USER QUERY ===\n", "{query}\n", "\n", "Intent:\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "# Optional: save to JSON\n", "intent_prompt.save(\"intent_classifier_prompt.json\")\n"]}, {"cell_type": "code", "execution_count": 1, "id": "1e500278", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "# Create the enhanced intent classifier prompt\n", "intent_prompt = PromptTemplate(\n", "    input_variables=[\"query\"],\n", "    template=\"\"\"\n", "You are an intent classifier for a Project Requirement Document (PRD) assistant.\n", "\n", "Your job is to classify the user's query into one of the following intents:\n", "\n", "- greeting → If the user is saying hello, hi, good morning, how are you, etc.\n", "- generate_prd → If the user wants to create or generate a NEW PRD from uploaded document content for the first time.\n", "- modify_prd → If the user wants to modify, update, change, revise, edit, or enhance an EXISTING PRD.\n", "- document_query → If the user is asking questions, requesting analysis, or seeking information about the uploaded documents.\n", "- unknown → If the intent is unclear, irrelevant, or doesn't fit any category.\n", "\n", "=== INTENT BOUNDARIES ===\n", "\n", "**generate_prd** examples:\n", "• \"Create a PRD from these documents\"\n", "• \"Generate a project requirements document\"\n", "• \"Build a PRD based on this content\"\n", "• \"Make a PRD from the uploaded files\"\n", "\n", "**modify_prd** examples:\n", "• \"Update the timeline in the PRD\"\n", "• \"Change the stakeholders section\"\n", "• \"Make the PRD more concise\"\n", "• \"Add more details to requirements\"\n", "• \"Revise the scope section\"\n", "\n", "**document_query** examples:\n", "• \"Summarize these documents\"\n", "• \"What are the key points in the files?\"\n", "• \"Tell me about the project described here\"\n", "• \"What technologies are mentioned?\"\n", "• \"Analyze the requirements in the document\"\n", "• \"Extract the main features discussed\"\n", "• \"What is the timeline mentioned?\"\n", "• \"Who are the stakeholders?\"\n", "• \"Give me an overview of the content\"\n", "• \"What are the risks identified?\"\n", "\n", "=== MODIFICATION INTENT HINTS ===\n", "The query is likely `modify_prd` if it includes:\n", "• Words like: modify, change, update, revise, edit, enhance, improve, add to, remove from, adjust  \n", "• References to specific PRD sections: \"update timeline\", \"change requirements\", \"modify stakeholders\"  \n", "• Iterative phrasing: \"also add\", \"now include\", \"further enhance\", \"make it more detailed\"\n", "\n", "=== DOCUMENT QUERY HINTS ===\n", "The query is likely `document_query` if it includes:\n", "• Question words: what, who, where, when, why, how\n", "• Analysis requests: summarize, analyze, extract, identify, list, describe\n", "• Information seeking: tell me about, explain, overview, details about\n", "• Content exploration: key points, main features, important aspects\n", "\n", "=== OUTPUT INSTRUCTION ===\n", "Return only the intent name as one of:\n", "`greeting`, `generate_prd`, `modify_prd`, `document_query`, or `unknown`\n", "\n", "=== USER QUERY ===\n", "{query}\n", "\n", "Intent:\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "# Optional: save to JSON\n", "intent_prompt.save(\"intent_classifier_prompt.json\")"]}, {"cell_type": "code", "execution_count": 1, "id": "39d76a85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sumit\n"]}], "source": ["print(\"sumit\")"]}, {"cell_type": "code", "execution_count": null, "id": "e40b250d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}