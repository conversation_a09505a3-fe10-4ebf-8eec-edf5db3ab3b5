import random
import string
import httpx
import uuid
import logging
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>
from fastapi.responses import RedirectResponse
from dotenv import load_dotenv
from urllib.parse import urlencode
from app.core.config import settings
from app.database.models.user_model import User
from app.core.password import get_password_hash
from app.core.security import create_access_token, create_refresh_token
from app.services.email_service import send_email
from datetime import datetime
from app.database.models.plan_model import Plan
from app.database.models.user_uploads_model import UserUpload
from app.database.enum import KeyEnum
from app.services.api_keys_settings_service import APIKEYSETTINGService
 
load_dotenv()
class OAuthLoginService:

    @staticmethod
    def google_login_service(db):   
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.GOOGLE_OAUTH_KEYS)
        google_key = key["GOOGLE_CLIENT_ID"]
        if google_key:
            Google_Client_Id= google_key
        else:
            Google_Client_Id = settings.GOOGLE_CLIENT_ID
 
        google_auth_url = "https://accounts.google.com/o/oauth2/auth?" + urlencode({
            "client_id": Google_Client_Id,
            "response_type": "code",
            "redirect_uri": settings.REDIRECT_URI,
            "scope": settings.GOOGLE_SCOPE,
            "access_type": "offline",
            "prompt": "consent"
        })
        return {"login_url": google_auth_url}

    @staticmethod
    async def google_callback_service(code: str, db):
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.GOOGLE_OAUTH_KEYS)
        google_client_key = key["GOOGLE_CLIENT_ID"]
        google_secret_key = key["GOOGLE_CLIENT_SECRET"]
        if google_client_key and google_secret_key:
            Google_Client_Id= google_client_key
            Google_Client_Secret = google_secret_key
        else:
            Google_Client_Id = settings.GOOGLE_CLIENT_ID
            Google_Client_Secret = settings.GOOGLE_CLIENT_SECRET

        token_url = "https://oauth2.googleapis.com/token"
        async with httpx.AsyncClient() as client:
            token_response = await client.post(token_url, data={
                "client_id": Google_Client_Id,
                "client_secret": Google_Client_Secret,
                "code": code,
                "grant_type": "authorization_code",
                "redirect_uri": settings.REDIRECT_URI,
            })
            if token_response.status_code != 200:
                raise HTTPException(status_code=400, detail="Failed to obtain access token")
            token_data = token_response.json()
            access_token = token_data.get("access_token")
            if not access_token:
                raise HTTPException(status_code=400, detail="Invalid response from Google")
            user_info_url = "https://www.googleapis.com/oauth2/v2/userinfo"
            user_response = await client.get(user_info_url, headers={
                "Authorization": f"Bearer {access_token}"
            })
            if user_response.status_code != 200:
                raise HTTPException(status_code=400, detail="Failed to fetch user info")
            user_info = user_response.json()
            email = user_info.get("email")
            name = user_info.get("name")
            existing_user = db.query(User).filter(User.email == email).first()
            if existing_user.is_active == False:
                raise HTTPException(status_code=400, detail="Your account is Inactive.Please contact with admin to activate your account.")

            if not existing_user:
                generated_password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
                hashed_password = get_password_hash(generated_password)
                new_user = User(email=email,name=name,password=hashed_password,is_active=True,role="User",platform="Google",last_login=datetime.now() )
                db.add(new_user)
                db.commit()
                db.refresh(new_user)
                user_id = new_user.id


                free_plan = db.query(Plan).filter(Plan.price == 0).first()
                if free_plan:
                    user_uploads = UserUpload(
                        user_id=new_user.id,
                        remaining_uploads=free_plan.uploads,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    db.add(user_uploads)
                    db.commit()

                email_body = f"""
                    <h3>Welcome {name}!</h3>
                    <p>Your account has been created via Google login.</p>
                    <p>Your temporary password is: <b>{generated_password}</b></p>
                    <p>Please change it after logging in.</p>
                """
                try:
                    await send_email(email, "Your Account Details", email_body)
                except Exception as e:
                    print(f"Failed to send email to {email}: {e}")
                message = "Account created successfully. You can log in using Google or with the password sent to your email."
            else:
                existing_user.last_login = datetime.now()
                platform = "Google"
                db.commit()
                new_user = existing_user
                user_id = new_user.id
                message = "Login successful."
        jwt_token = create_access_token({
            "sub": new_user.email,
            "role": str(new_user.role.name) if hasattr(new_user.role, "name") else new_user.role,
            "id": user_id
        })
        refresh_token = create_refresh_token({
            "sub": new_user.email,
            "role": str(new_user.role.name) if hasattr(new_user.role, "name") else new_user.role,
            "id": user_id
        })
        return RedirectResponse(
            url=f"{settings.FRONTEND_URL}/success?access_token={jwt_token}&refresh_token={refresh_token}&message={message}"
        )


    @staticmethod
    def github_login_service(db):   
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.GITHUB_OAUTH_KEYS) 
        github_client_key = key["GITHUB_CLIENT_ID"]
        if github_client_key:
            Github_Client_Id= github_client_key
        else:
            Github_Client_Id = settings.GITHUB_CLIENT_ID       

        github_auth_url = "https://github.com/login/oauth/authorize?" + urlencode({
            "client_id": Github_Client_Id,
            "redirect_uri": settings.GITHUB_REDIRECT_URI,
            "scope": "user:email",
            "allow_signup": "false"
        })
        return {"login_url": github_auth_url}
    
    @staticmethod
    async def github_callback_service(code: str, db): 
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.GITHUB_OAUTH_KEYS) 
        github_client_key = key["GITHUB_CLIENT_ID"]
        github_secret_key = key["GITHUB_CLIENT_SECRET"]
        if github_client_key and github_secret_key:
            Github_Client_Id= github_client_key
            Github_Client_Secret = github_secret_key
        else:
            Github_Client_Id = settings.GITHUB_CLIENT_ID  
            Github_Client_Secret = settings.GITHUB_CLIENT_SECRET        

        token_url = "https://github.com/login/oauth/access_token"
        headers = {"Accept": "application/json"}

        async with httpx.AsyncClient() as client:
            token_response = await client.post(token_url, data={
                "client_id": Github_Client_Id,
                "client_secret": Github_Client_Secret,
                "code": code,
                "redirect_uri": settings.GITHUB_REDIRECT_URI,
            }, headers=headers)

            if token_response.status_code != 200:
                raise HTTPException(status_code=400, detail="Failed to obtain GitHub access token")

            token_data = token_response.json()
            access_token = token_data.get("access_token")
            if not access_token:
                raise HTTPException(status_code=400, detail="Invalid GitHub token response")

            user_info_url = "https://api.github.com/user"
            email_info_url = "https://api.github.com/user/emails"

            user_response = await client.get(user_info_url, headers={
                "Authorization": f"token {access_token}"
            })

            email_response = await client.get(email_info_url, headers={
                "Authorization": f"token {access_token}"
            })

            if user_response.status_code != 200 or email_response.status_code != 200:
                raise HTTPException(status_code=400, detail="Failed to fetch GitHub user info")

            user_data = user_response.json()
            email_data = email_response.json()

            primary_email = next((email["email"] for email in email_data if email.get("primary")), None)
            if not primary_email:
                raise HTTPException(status_code=400, detail="GitHub email not available")

            name = user_data.get("name") or user_data.get("login")

            existing_user = db.query(User).filter(User.email == primary_email).first()
            if existing_user.is_active == False:
                raise HTTPException(status_code=400, detail="Your account is Inactive.Please contact with admin to activate your account.")
            if not existing_user:
                generated_password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
                hashed_password = get_password_hash(generated_password)
                new_user = User(
                    email=primary_email,
                    name=name,
                    password=hashed_password,
                    is_active=True,
                    role="User",
                    platform="GitHub",
                    last_login=datetime.now()
                )
                db.add(new_user)
                db.commit()
                db.refresh(new_user)
                user_id = new_user.id

                free_plan = db.query(Plan).filter(Plan.price == 0).first()
                if free_plan:
                    user_uploads = UserUpload(
                        user_id=new_user.id,
                        remaining_uploads=free_plan.uploads,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    db.add(user_uploads)
                    db.commit()

                email_body = f"""
                    <h3>Welcome {name}!</h3>
                    <p>Your account has been created via GitHub login.</p>
                    <p>Your temporary password is: <b>{generated_password}</b></p>
                    <p>Please change it after logging in.</p>
                """
                try:
                    await send_email(primary_email, "Your GitHub Login Details", email_body)
                except Exception as e:
                    print(f"Failed to send email to {primary_email}: {e}")

                message = "Account created via GitHub. Temporary password has been emailed."
            else:
                existing_user.last_login = datetime.now()
                existing_user.platform = "GitHub"
                db.commit()                
                new_user = existing_user
                user_id = new_user.id                
                message = "GitHub login successful."

        jwt_token = create_access_token({
            "sub": new_user.email,
            "role": str(new_user.role.name) if hasattr(new_user.role, "name") else new_user.role,
            "id": user_id
        })
        refresh_token = create_refresh_token({
            "sub": new_user.email,
            "role": str(new_user.role.name) if hasattr(new_user.role, "name") else new_user.role,
            "id": user_id
        })

        return RedirectResponse(
            url=f"{settings.FRONTEND_URL}/success?access_token={jwt_token}&refresh_token={refresh_token}&message={message}"
        )
    

    @staticmethod
    def microsoft_login_service():        
        auth_url = "https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/authorize?".format(
            tenant_id=settings.MICROSOFT_TENANT_ID
        )
        params = {
            "client_id": settings.MICROSOFT_CLIENT_ID,
            "response_type": "code",
            "redirect_uri": settings.MICROSOFT_REDIRECT_URI,
            "response_mode": "query",
            "scope": "User.Read openid email profile offline_access",
            "state": str(uuid.uuid4())
        }
        return {"login_url": auth_url + urlencode(params)}

    @staticmethod
    async def microsoft_callback_service(code: str, db):
        token_url = f"https://login.microsoftonline.com/{settings.MICROSOFT_TENANT_ID}/oauth2/v2.0/token"

        payload = {
            "client_id": settings.MICROSOFT_CLIENT_ID,
            "client_secret": settings.MICROSOFT_CLIENT_SECRET,
            "code": code,
            "redirect_uri": settings.MICROSOFT_REDIRECT_URI,
            "grant_type": "authorization_code"
        }

        logging.info("Attempting to exchange code for token with payload (without client_secret):")
        log_payload = {k: v for k, v in payload.items() if k != "client_secret"}
        logging.info(log_payload)

        async with httpx.AsyncClient() as client:
            token_response = await client.post(token_url, data=payload)

        if token_response.status_code != 200:
            logging.error(f"Microsoft token request failed with status {token_response.status_code}")
            logging.error(f"Response content: {token_response.text}")
            raise HTTPException(status_code=400, detail="Failed to get access token from Microsoft")

        token_data = token_response.json()
        access_token = token_data.get("access_token")

        logging.info("Access token received successfully.")

        async with httpx.AsyncClient() as client:
            user_response = await client.get(
                "https://graph.microsoft.com/v1.0/me",
                headers={"Authorization": f"Bearer {access_token}"}
            )

        if user_response.status_code != 200:
            logging.error(f"Failed to fetch user info. Status: {user_response.status_code}")
            logging.error(f"User info response: {user_response.text}")
            raise HTTPException(status_code=400, detail="Failed to fetch user info from Microsoft")

        user_info = user_response.json()
        email = user_info.get("mail") or user_info.get("userPrincipalName")
        name = user_info.get("displayName")

        logging.info(f"User info fetched: {email}, {name}")

        existing_user = db.query(User).filter(User.email == email).first()
        if not existing_user:
            import random, string
            temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
            hashed_password = get_password_hash(temp_password)

            new_user = User(
                email=email,
                name=name,
                password=hashed_password,
                is_active=True,
                last_login=datetime.now(),
                role="User",
                platform="Microsoft"
            )

            db.add(new_user)
            db.commit()
            db.refresh(new_user)

            logging.info(f"New user created: {email}")

            email_body = f"""
                <h3>Welcome {name}!</h3>
                <p>Your account has been created via Microsoft login.</p>
                <p>Your temporary password is: <b>{temp_password}</b></p>
                <p>Please change it after logging in.</p>
            """
            try:
                await send_email(email, "Welcome to the Platform!", email_body)
                logging.info(f"Welcome email sent to: {email}")
            except Exception as e:
                logging.error(f"Failed to send email to {email}: {str(e)}")

            message = "Account created successfully."
            user_id = new_user.id
        else:
            existing_user.last_login = datetime.now()
            existing_user.platform = "Microsoft"
            db.commit()
            logging.info(f"Existing user logged in: {email}")
            new_user = existing_user
            user_id = new_user.id
            message = "Login successful."

        jwt_token = create_access_token({
            "sub": new_user.email,
            "role": str(new_user.role.name) if hasattr(new_user.role, "name") else new_user.role,
            "id": user_id
        })
        refresh_token = create_refresh_token({
            "sub": new_user.email,
            "role": str(new_user.role.name) if hasattr(new_user.role, "name") else new_user.role,
            "id": user_id
        })

        logging.info(f"JWT and refresh tokens created for: {email}")

        return RedirectResponse(
            url=f"{settings.FRONTEND_URL}/success?access_token={jwt_token}&refresh_token={refresh_token}&message={message}"
        )