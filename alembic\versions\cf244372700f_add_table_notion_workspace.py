"""Add Table Notion Workspace

Revision ID: cf244372700f
Revises: 94447622361e
Create Date: 2025-06-10 10:31:55.131411

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cf244372700f'
down_revision: Union[str, None] = '94447622361e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notion_tokens',
    sa.Column('workspace_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('access_token', sa.String(), nullable=True),
    sa.Column('bot_id', sa.String(), nullable=True),
    sa.Column('duplicated_template_id', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('workspace_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by <PERSON><PERSON>bic - please adjust! ###
    op.drop_table('notion_tokens')
    # ### end Alembic commands ###
