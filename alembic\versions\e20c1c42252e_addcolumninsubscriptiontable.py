"""addcolumninsubscriptiontable

Revision ID: e20c1c42252e
Revises: cd56ea0c6943
Create Date: 2025-05-01 15:38:09.755528

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e20c1c42252e'
down_revision: Union[str, None] = 'cd56ea0c6943'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscriptions', sa.Column('expiry_date', sa.DateTime(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscriptions', 'expiry_date')
    # ### end Alembic commands ###
