"""addcolumninpaymnettable

Revision ID: abf12497ab17
Revises: e20c1c42252e
Create Date: 2025-05-01 15:41:12.476552

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'abf12497ab17'
down_revision: Union[str, None] = 'e20c1c42252e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscriptions', sa.Column('expiry_date', sa.DateTime(), server_default=sa.text('now()'), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscriptions', 'expiry_date')
    # ### end Alembic commands ###
