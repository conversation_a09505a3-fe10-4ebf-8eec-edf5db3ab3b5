#!/usr/bin/env python3
"""
Test script to understand intent classification behavior
"""

def test_intent_examples():
    """
    Examples of different user inputs and their expected intents
    """
    
    test_cases = [
        # Generate PRD intents
        {
            "input": "Generate a comprehensive PRD",
            "expected": "generate_prd",
            "description": "Basic PRD generation request"
        },
        {
            "input": "Create a product requirements document",
            "expected": "generate_prd", 
            "description": "Alternative PRD generation phrasing"
        },
        {
            "input": "Generate PRD for AI voice assistant",
            "expected": "generate_prd",
            "description": "Specific PRD generation request"
        },
        
        # Modify PRD intents
        {
            "input": "Update the PRD to include mobile support",
            "expected": "modify_prd",
            "description": "PRD modification request"
        },
        {
            "input": "Modify the requirements to add security features",
            "expected": "modify_prd",
            "description": "Requirements modification"
        },
        {
            "input": "Change the PRD to focus on enterprise users",
            "expected": "modify_prd",
            "description": "PRD focus change"
        },
        {
            "input": "Add more details about the API endpoints",
            "expected": "modify_prd",
            "description": "Adding details to existing PRD"
        },
        
        # Document query intents
        {
            "input": "What are the main features mentioned in the document?",
            "expected": "document_query",
            "description": "Question about document content"
        },
        {
            "input": "Summarize the key requirements",
            "expected": "document_query",
            "description": "Summarization request"
        },
        {
            "input": "What technologies are mentioned?",
            "expected": "document_query",
            "description": "Specific information extraction"
        },
        {
            "input": "Explain the business objectives",
            "expected": "document_query",
            "description": "Explanation request"
        },
        
        # Greeting intents
        {
            "input": "Hello",
            "expected": "greeting",
            "description": "Simple greeting"
        },
        {
            "input": "Hi there",
            "expected": "greeting",
            "description": "Casual greeting"
        },
        {
            "input": "Good morning",
            "expected": "greeting",
            "description": "Time-specific greeting"
        },
        
        # Unknown intents
        {
            "input": "What's the weather like?",
            "expected": "unknown",
            "description": "Unrelated question"
        },
        {
            "input": "How do I cook pasta?",
            "expected": "unknown",
            "description": "Completely unrelated query"
        }
    ]
    
    print("🧪 Intent Classification Test Cases")
    print("=" * 60)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['description']}")
        print(f"   Input: '{case['input']}'")
        print(f"   Expected Intent: {case['expected']}")
        
        # You can test these by sending them to your streaming endpoint
        print(f"   Test Command:")
        print(f"   curl -X POST 'http://localhost:8000/api/v1/prd_upload/upload_files_stream' \\")
        print(f"        -F 'user_input={case['input']}' \\")
        print(f"        -F 'files=@test.txt'")

def debug_intent_issues():
    """
    Common issues with intent classification
    """
    print("\n" + "=" * 60)
    print("🔍 Common Intent Classification Issues")
    print("=" * 60)
    
    issues = [
        {
            "issue": "Intent always returns 'generate_prd'",
            "causes": [
                "Intent classifier prompt is too broad",
                "Training examples favor PRD generation",
                "User input is ambiguous"
            ],
            "solutions": [
                "Check intent_classifier_prompt.json",
                "Add more specific examples for each intent",
                "Make user input more explicit"
            ]
        },
        {
            "issue": "modify_prd not working correctly",
            "causes": [
                "No previous PRD in chat history",
                "Chat history not properly formatted",
                "Modification prompt missing context"
            ],
            "solutions": [
                "Ensure chat history contains previous AI responses",
                "Check recent_prd parameter in modification_chain",
                "Verify modification_history_messages extraction"
            ]
        },
        {
            "issue": "document_query returns generic responses",
            "causes": [
                "Document query prompt too generic",
                "Extracted text not properly passed",
                "Query context missing"
            ],
            "solutions": [
                "Check document_query_prompt.json",
                "Verify extracted_text parameter",
                "Add more specific query handling"
            ]
        }
    ]
    
    for issue_data in issues:
        print(f"\n❌ Issue: {issue_data['issue']}")
        print("   Possible Causes:")
        for cause in issue_data['causes']:
            print(f"     • {cause}")
        print("   Solutions:")
        for solution in issue_data['solutions']:
            print(f"     ✅ {solution}")

def debug_chat_history():
    """
    Debug chat history for modify_prd intent
    """
    print("\n" + "=" * 60)
    print("💬 Chat History Debug for modify_prd")
    print("=" * 60)
    
    print("""
For modify_prd to work correctly, you need:

1. Previous AI messages in chat_history:
   - At least one AIMessage with PRD content
   - Recent conversation context

2. Proper chat_history format:
   - List of HumanMessage and AIMessage objects
   - Chronological order (oldest to newest)

3. modification_history_messages extraction:
   - Gets content from last 3 AI messages
   - Used to understand previous PRD versions

Example chat_history for modify_prd:
[
    HumanMessage(content="Generate a PRD for mobile app"),
    AIMessage(content="# PRD for Mobile App\\n\\n## Overview\\n..."),
    HumanMessage(content="Add security features to the PRD"),  # This triggers modify_prd
]

Debug in your breakpoint:
- Check len(chat_history)
- Check types: [type(msg) for msg in chat_history]
- Check AI messages: [msg.content for msg in chat_history if isinstance(msg, AIMessage)]
""")

if __name__ == "__main__":
    test_intent_examples()
    debug_intent_issues()
    debug_chat_history()
    
    print("\n" + "=" * 60)
    print("🚀 Next Steps:")
    print("=" * 60)
    print("1. Test with the example inputs above")
    print("2. Check the debug logs in your server")
    print("3. Use the breakpoint to inspect:")
    print("   - intent variable value")
    print("   - chat_history contents")
    print("   - user_input processing")
    print("4. Verify prompt files exist and are properly formatted")
