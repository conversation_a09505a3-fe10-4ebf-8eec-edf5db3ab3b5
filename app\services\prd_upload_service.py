import os
import openai
import tempfile
import pytesseract
from PIL import Image
from uuid import uuid4
from typing import List, <PERSON><PERSON>, AsyncGenerator, Dict, Any
from app.database.models.user_model import User
from app.database.models.user_uploads_model import UserUpload
from app.database.models.guestusage import GuestUsage
from app.core.config import settings
from fastapi import HTTPException, status
from datetime import datetime, timedelta
from app.database.models.chat_session_model import ChatSession
from app.database.models.chat_history_model import ChatHistory
from app.database.models.subscriptions_model import Subscription
from pathlib import Path
from fastapi import UploadFile
from sqlalchemy.orm import Session
from langchain_openai import ChatOpenAI
from langchain_core.prompts import load_prompt
from langchain_community.document_loaders import PyPDFLoader, Docx2txtLoader, TextLoader
import re
from langchain.schema import HumanMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema.output_parser import StrOutputParser
from langchain.schema.messages import BaseMessage
from typing import Optional
from langchain_core.prompts import PromptTemplate
import shutil
import subprocess
import logging
import time
import re
from tenacity import retry, stop_after_attempt, wait_exponential
from app.services.api_keys_settings_service import APIKEYSETTINGService
from app.database.enum import KeyEnum
import tiktoken
from app.database.models.extracted_file_text_model import ExtractedFileText
import asyncio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PrdUploadService:

    OPTIMAL_CHUNK_SIZE = settings.OPTIMAL_CHUNK_SIZE
    MAX_WORKERS = settings.MAX_WORKERS
    FFMPEG_PRESET = settings.FFMPEG_PRESET
    BATCH_SIZE = settings.BATCH_SIZE
    RETRY_DELAY = settings.RETRY_DELAY
    MAX_CHUNK_SIZE_MB = 20  
    WHISPER_TIMEOUT = 300  
    MAX_VIDEO_SIZE_MB = 1500  
    
    
    @staticmethod
    def handle_guest_user(db: Session, client_ip: str):
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.GUEST_USER_LIMIT)
        guest_window_minutes = key.get("GUEST_WINDOW_MINUTES") if key else None
        max_uploads_per_ip = key.get("MAX_UPLOADS_PER_IP") if key else None
        
        if guest_window_minutes and max_uploads_per_ip:
            GUEST_WINDOW_MINUTES =int(guest_window_minutes)
            MAX_UPLOADS_PER_IP = int(max_uploads_per_ip)
        else:
            GUEST_WINDOW_MINUTES = settings.GUEST_WINDOW_MINUTES
            MAX_UPLOADS_PER_IP = settings.MAX_UPLOADS_PER_IP
        
        now = datetime.now()
        usage = db.query(GuestUsage).filter_by(ip_address=client_ip).first()

        if usage:
            if now - usage.last_accessed > timedelta(minutes=GUEST_WINDOW_MINUTES):
                usage.upload_count = 1
            elif usage.upload_count >= MAX_UPLOADS_PER_IP:
                raise HTTPException(
                    status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                    detail="Free usage limit exceeded. Please login to enjoy services."
                )
            else:
                usage.upload_count += 1
            usage.last_accessed = now
        else:
            usage = GuestUsage(ip_address=client_ip, upload_count=1, last_accessed=now)

        db.add(usage)
        db.commit()

    @staticmethod
    def validate_authenticated_user(db: Session, current_user: User):
        user_uploads = db.query(UserUpload).filter(UserUpload.user_id == current_user.id).first()
        subscription = db.query(Subscription).filter(Subscription.user_id == current_user.id).first()

        if not user_uploads:
            raise HTTPException(status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION, detail="Uploads not found.")

        if subscription and subscription.expiry_date < datetime.now():
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="Your subscription has expired. Please renew your subscription to use this feature."
            )

        if user_uploads.remaining_uploads <= 0:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="Please upgrade your plan to continue using this feature."
            )

    @staticmethod
    def handle_session(db: Session, session_id: Optional[str], current_user: Optional[User], files: List[UploadFile]) -> str:
        user_id = current_user.id if current_user else None

        if session_id is None:
            session_id = str(uuid4())
            db.add(ChatSession(session_id=session_id, user_id=user_id, created_at=datetime.now(), updated_at=datetime.now()))
            db.commit()
        else:
            existing_history = db.query(ChatHistory).filter_by(session_id=session_id).first()
            if not existing_history and not files:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No files found in session history.")

        return session_id

    @staticmethod
    def decrement_user_upload_count(db: Session, current_user: User):
        user_uploads = db.query(UserUpload).filter(UserUpload.user_id == current_user.id).first()
        if user_uploads and user_uploads.remaining_uploads > 0:
            user_uploads.remaining_uploads -= 1
            db.commit()
            db.refresh(user_uploads) 

    @staticmethod
    def extract_audio_from_video_optimized(video_path: str, output_audio_path: str) -> str:
        """Ultra-optimized audio extraction with better error handling"""
        try:
            if os.path.exists(output_audio_path):
                os.remove(output_audio_path)

            command = [
                "ffmpeg",
                "-i", video_path,
                "-vn",                          
                "-ac", "1",                     
                "-ar", "16000",                 
                "-acodec", "libmp3lame",        
                "-b:a", "64k",                  
                "-f", "mp3",                    
                "-preset", "ultrafast",
                "-threads", str(min(os.cpu_count(), 4)),  
                "-y",                          
                output_audio_path
            ]
            
            logger.info(f"Extracting audio from {video_path}")
            start_time = time.time()
            
            result = subprocess.run(
                command, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE, 
                text=True,
                timeout=300  
            )
            
            elapsed = time.time() - start_time
            logger.info(f"Audio extraction completed in {elapsed:.2f}s")
            
            if result.returncode != 0:
                raise RuntimeError(f"FFmpeg error: {result.stderr}")
                
            return output_audio_path
            
        except subprocess.TimeoutExpired:
            raise Exception("Audio extraction timed out after 5 minutes")
        except Exception as e:
            raise Exception(f"Error extracting audio: {str(e)}")

    @staticmethod
    def create_smart_chunks(audio_path: str) -> Tuple[List[str], str]:
        """Create optimized chunks with better size management"""
        try:
            file_size = os.path.getsize(audio_path)
            file_size_mb = file_size / (1024 * 1024)
            temp_dir = tempfile.mkdtemp()
            
            logger.info(f"Audio file size: {file_size_mb:.2f} MB")
            
            if file_size_mb <= PrdUploadService.MAX_CHUNK_SIZE_MB:
                logger.info("File small enough, processing as single chunk")
                return [audio_path], temp_dir
            
            duration_cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration", 
                "-of", "csv=p=0", audio_path
            ]
            
            try:
                duration_result = subprocess.run(duration_cmd, capture_output=True, text=True, timeout=30)
                total_duration = float(duration_result.stdout.strip())
                target_chunks = max(2, int(file_size_mb / PrdUploadService.MAX_CHUNK_SIZE_MB))
                chunk_duration = total_duration / target_chunks
                
                logger.info(f"Total duration: {total_duration:.2f}s, Target chunks: {target_chunks}")
                
            except (subprocess.TimeoutExpired, ValueError, subprocess.CalledProcessError):
                chunk_duration = 600  
                logger.warning("Could not determine duration, using default chunk size")
            
            chunk_paths = []
            chunk_index = 0
            
            while True:
                start_time = chunk_index * chunk_duration
                chunk_path = os.path.join(temp_dir, f"chunk_{chunk_index:03d}.mp3")
                
                command = [
                    "ffmpeg",
                    "-i", audio_path,
                    "-ss", str(start_time),
                    "-t", str(chunk_duration),
                    "-ac", "1",
                    "-ar", "16000",
                    "-acodec", "libmp3lame",
                    "-b:a", "64k",
                    "-f", "mp3",
                    "-y",
                    chunk_path
                ]
                
                result = subprocess.run(
                    command, 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    timeout=60 
                )
                
                if result.returncode == 0 and os.path.exists(chunk_path):
                    chunk_size = os.path.getsize(chunk_path)
                    chunk_size_mb = chunk_size / (1024 * 1024)
                    
                    if chunk_size > 50000:  
                        if chunk_size_mb <= 24:  
                            chunk_paths.append(chunk_path)
                            logger.info(f"Created chunk {chunk_index}: {chunk_size_mb:.2f} MB")
                            chunk_index += 1
                        else:
                            logger.warning(f"Chunk {chunk_index} too large ({chunk_size_mb:.2f} MB), skipping")
                            os.remove(chunk_path)
                            break
                    else:
                        os.remove(chunk_path)
                        break
                else:
                    break
            
            logger.info(f"Created {len(chunk_paths)} chunks")
            return chunk_paths, temp_dir
            
        except Exception as e:
            raise Exception(f"Error creating chunks: {str(e)}")

    @staticmethod
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=2, min=3, max=15))
    def transcribe_audio_chunk_with_retry(chunk_path: str) -> str:
        """Enhanced retry logic with better error handling"""
        try:
            file_size = os.path.getsize(chunk_path)
            file_size_mb = file_size / (1024 * 1024)
            
            if file_size == 0:
                logger.warning(f"Empty chunk file: {chunk_path}")
                return ""
            
            if file_size_mb > 25:  
                logger.error(f"Chunk too large ({file_size_mb:.1f}MB): {chunk_path}")
                return ""
            
            logger.info(f"Transcribing {os.path.basename(chunk_path)} ({file_size_mb:.1f}MB)")
            start_time = time.time()
            
            # # Fixed API key retrieval
            # key = APIKEYSETTINGService.get_api_key_by_name(db,KeyEnum.OPENAI_API_KEYS)
            # openai_api_key = key.get("OPENAI_API_KEY") if key else None
            # 
            # if openai_api_key:
            #     openai.api_key = openai_api_key
            # else:
            # openai.api_key = settings.OPENAI_API_KEY
            
            client = openai.OpenAI(timeout=PrdUploadService.WHISPER_TIMEOUT)
            
            with open(chunk_path, "rb") as audio_file:
                result = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    response_format="text",
                    language="en",
                    temperature=0
                )
            
            elapsed = time.time() - start_time
            transcript = result.strip() if result else ""
            logger.info(f"Transcribed {os.path.basename(chunk_path)}: {len(transcript)} chars in {elapsed:.2f}s")
            
            return transcript
            
        except Exception as e:
            logger.error(f"Transcription failed for {chunk_path}: {str(e)}")
            raise

    @staticmethod
    def sequential_transcribe_with_delays(chunk_paths: List[str]) -> List[str]:
        """Sequential processing with delays to avoid rate limits"""
        if not chunk_paths:
            return []
        
        logger.info(f"Starting sequential transcription of {len(chunk_paths)} chunks")
        transcripts = []
        
        for i, chunk_path in enumerate(chunk_paths):
            try:
                logger.info(f"Processing chunk {i+1}/{len(chunk_paths)}")
                transcript = PrdUploadService.transcribe_audio_chunk_with_retry(chunk_path)
                transcripts.append(transcript)
                
                if i < len(chunk_paths) - 1: 
                    delay = PrdUploadService.RETRY_DELAY + (i * 0.5)  
                    logger.info(f"Waiting {delay:.1f}s before next request...")
                    time.sleep(delay)
                    
            except Exception as e:
                logger.error(f"Failed to transcribe chunk {i+1}: {str(e)}")
                transcripts.append("")
        
        successful_transcripts = [t for t in transcripts if t]
        logger.info(f"Sequential transcription completed: {len(successful_transcripts)}/{len(chunk_paths)} successful")
        return transcripts

    @staticmethod
    def clean_and_merge_transcripts_optimized(transcripts: List[str]) -> str:
        """Optimized transcript cleaning"""
        valid_transcripts = [t.strip() for t in transcripts if t and t.strip()]
        
        if not valid_transcripts:
            return "No valid transcripts found."        
        combined = " ".join(valid_transcripts)
        combined = re.sub(r'\s+', ' ', combined)        
        combined = re.sub(r'\.(?=[A-Z])', '. ', combined)
        combined = re.sub(r'([.!?])\s*([A-Z])', r'\1 \2', combined)        
        if combined:
            combined = combined[0].upper() + combined[1:] if len(combined) > 1 else combined.upper()        
        logger.info(f"Final transcript length: {len(combined)} characters")
        return combined

    @staticmethod
    def extract_text_from_video_optimized(video_path: str) -> str:
        """Optimized video processing with better resource management"""
        temp_audio_path = None
        temp_dir = None        
        try:
            if not os.path.exists(video_path):
                raise Exception(f"Video file not found: {video_path}")
            video_size_mb = os.path.getsize(video_path) / (1024 * 1024)
            logger.info(f"Processing video: {video_path} ({video_size_mb:.2f} MB)")
            if video_size_mb > PrdUploadService.MAX_VIDEO_SIZE_MB:
                return f"Video file too large for processing (max {PrdUploadService.MAX_VIDEO_SIZE_MB}MB). For 3+ hour videos, please ensure good compression or split into segments."
            start_time = time.time()
            temp_audio_path = tempfile.mktemp(suffix=".mp3")
            PrdUploadService.extract_audio_from_video_optimized(video_path, temp_audio_path)
            chunk_paths, temp_dir = PrdUploadService.create_smart_chunks(temp_audio_path)
            if len(chunk_paths) == 1 and chunk_paths[0] == temp_audio_path:
                logger.info("Processing as single file")
                transcript = PrdUploadService.transcribe_audio_chunk_with_retry(temp_audio_path)
                transcripts = [transcript] if transcript else []
            else:
                logger.info(f"Processing {len(chunk_paths)} chunks sequentially")
                transcripts = PrdUploadService.sequential_transcribe_with_delays(chunk_paths)
            final_transcript = PrdUploadService.clean_and_merge_transcripts_optimized(transcripts)
            elapsed = time.time() - start_time
            logger.info(f"Total video processing time: {elapsed:.2f}s")
            return final_transcript
        except Exception as e:
            logger.error(f"Error processing video {video_path}: {str(e)}")
            return f"Unable to extract audio content from video file. Please ensure the video file is not corrupted and contains audio. Error details: {str(e)}"
        finally:
            cleanup_paths = []
            if temp_audio_path and os.path.exists(temp_audio_path):
                cleanup_paths.append(temp_audio_path)
            if temp_dir and os.path.exists(temp_dir):
                cleanup_paths.append(temp_dir)
            
            for path in cleanup_paths:
                try:
                    if os.path.isfile(path):
                        os.remove(path)
                    elif os.path.isdir(path):
                        shutil.rmtree(path)
                    logger.info(f"Cleaned up: {path}")
                except Exception as cleanup_error:
                    logger.warning(f"Cleanup error for {path}: {cleanup_error}")

    @staticmethod
    def extract_text_from_audio_openai_optimized(audio_path: str) -> str:
        """Optimized audio transcription"""
        try:
            file_size = os.path.getsize(audio_path)
            file_size_mb = file_size / (1024 * 1024)
            
            logger.info(f"Processing audio file: {audio_path} ({file_size_mb:.2f} MB)")
            
            if file_size_mb > PrdUploadService.MAX_CHUNK_SIZE_MB:
                chunk_paths, temp_dir = PrdUploadService.create_smart_chunks(audio_path)
                try:
                    transcripts = PrdUploadService.sequential_transcribe_with_delays(chunk_paths)
                    return PrdUploadService.clean_and_merge_transcripts_optimized(transcripts)
                finally:
                    if temp_dir and os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
            else:
                return PrdUploadService.transcribe_audio_chunk_with_retry(audio_path)
                
        except Exception as e:
            logger.error(f"Error processing audio {audio_path}: {str(e)}")
            return f"Error processing audio: {str(e)}"

    @staticmethod
    def extract_text_from_single_file(file_path: str, filename: str) -> str:
        """Extract text from a single file based on its extension"""
        ext = Path(file_path).suffix.lower()
        text = ""
        
        try:
            if ext == '.pdf':
                loader = PyPDFLoader(file_path)
                text = "\n".join([page.page_content for page in loader.load()])
            elif ext == '.docx':
                loader = Docx2txtLoader(file_path)
                docs = loader.load()
                text = "\n".join([doc.page_content for doc in docs])
            elif ext == '.txt':
                loader = TextLoader(file_path, encoding='utf-8')
                docs = loader.load()
                text = "\n".join([doc.page_content for doc in docs])
            elif ext in ['.jpg', '.jpeg', '.png']:
                image = Image.open(file_path)
                text = pytesseract.image_to_string(image)
            elif ext in ['.mp3', '.wav', '.m4a']:
                text = PrdUploadService.extract_text_from_audio_openai_optimized(file_path)
            elif ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']:
                text = PrdUploadService.extract_text_from_video_optimized(file_path)
            else:
                text = "Unsupported file format."
                
            if not text or text.strip() == "":
                text = "No readable content found in file."
                
        except Exception as e:
            logger.error(f"Error extracting text from {filename}: {str(e)}")
            text = f"Error extracting text from {filename}: {str(e)}"
            
        return text

    @staticmethod
    def normalize_headings(text: str) -> str:
        lines = text.splitlines()
        normalized = []
        
        for line in lines:
            line = line.strip()
            if re.match(r"^#+\s*\d+\..+", line):
                clean = re.sub(r"^#+\s*", "", line).strip()
                normalized.append(f"**{clean}**")
            elif re.match(r"^#+\s*[A-Za-z]", line):
                clean = re.sub(r"^#+\s*", "", line).strip()
                normalized.append(f"**{clean}**")
            elif re.match(r"\*\*\d+\..+\*\*", line):
                clean = re.sub(r"^\*{2}|\*{2}$", "", line).strip()
                normalized.append(f"**{clean}**")
            elif re.match(r"\*\*[A-Za-z\s\-()]+[:]*\*\*", line):
                clean = re.sub(r"^\*{2}|\*{2}$", "", line).strip()
                normalized.append(f"**{clean}**")
            else:
                normalized.append(line)
                
        return "\n".join(normalized)

    @staticmethod
    def save_temp_file(file: UploadFile) -> tuple:
        suffix = Path(file.filename).suffix
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
            content = file.file.read()
            temp_file.write(content)
            temp_file.flush()
            return Path(temp_file.name), content
    
    @staticmethod
    def process_image(file_name: str, content: bytes) -> str:
        ext = Path(file_name).suffix
        image_filename = f"{uuid4()}{ext}"
        relative_path = f"{image_filename}"
        full_path = Path("static/prd_image_uploads") / relative_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        with open(full_path, "wb") as f:
            f.write(content)
        return f"/static/prd_image_uploads/{relative_path}"
    
    @staticmethod
    def extract_and_format_text(file_path: Path, filename: str) -> str:
        text = PrdUploadService.extract_text_from_single_file(str(file_path), filename)
        return f"\n\n--- Start of {filename} ---\n{text}\n--- End of {filename} ---\n"
    

    @staticmethod
    def classify_complexity_by_token_count(input_token_count: int) -> tuple:
        """
        Classify the complexity and set expected output token range with four levels.
        """
        if input_token_count <= 800:
            return "MINIMAL", "400-600"
        elif input_token_count <= 1600:
            return "BRIEF", "800-1200"
        elif input_token_count <= 3000:
            return "MODERATE", "1300-2000"
        else:
            return "DETAILED", "2600-4000"
        
    @staticmethod
    def count_tokens(text: str) -> int:
        """Count tokens in text using tiktoken"""
        encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
        return len(encoding.encode(text))
    
    @staticmethod
    def generate_prd_output(text: str, chat_history: List[BaseMessage], user_input: str,db: Session,session_id: str) -> str:
        
        if not text:
            stored = (
                db.query(ExtractedFileText)
                .filter(ExtractedFileText.session_id == session_id)
                .order_by(ExtractedFileText.created_at.desc())
                .first()
            )
            text = stored.extracted_text if stored else ""
        chat_template = ChatPromptTemplate.from_messages([
            ("system", "You are an AI assistant for writing PRDs from extracted documents."),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}")
        ])
        llm = ChatOpenAI(model="gpt-4o")        
        input_token_count = PrdUploadService.count_tokens(text)
        complexity_level, target_token_range = PrdUploadService.classify_complexity_by_token_count(input_token_count)
        try:
            persona_prompt_template = load_prompt("prompt/persona_prompt.json")
            intent_prompt_template = load_prompt("prompt/intent_classifier_prompt.json")
            modification_prompt_template = load_prompt("prompt/modification_template.json")
            prompt_template = load_prompt("prompt/prd_generation_template.json")
            document_query_prompt_template = load_prompt("prompt/document_query_prompt.json")
        except FileNotFoundError as e:
            logger.error(f"Prompt file not found: {e}")
            chain = chat_template | llm | StrOutputParser()
            if not user_input:
                user_input = "Generate a PRD"
            result = chain.invoke({
                "chat_history": chat_history, 
                "input": f"Based on the following text, generate a comprehensive PRD:\n\n{text}\n\nUser instruction: {user_input}"
            })
            return PrdUploadService.normalize_headings(result)
        persona_chain = persona_prompt_template | llm | StrOutputParser()
        intent_chain = intent_prompt_template | llm | StrOutputParser()
        modification_chain = modification_prompt_template | llm | StrOutputParser()
        document_query_chain = document_query_prompt_template | llm | StrOutputParser()
        
        persona = persona_chain.invoke({"extracted_text": text})
        chain = chat_template | llm | StrOutputParser()
        if not user_input:
            user_input = "Generate a PRD"
        intent = intent_chain.invoke({"query": user_input})
        if intent == "generate_prd":
            current_input = prompt_template.invoke({
                "extracted_text": text,
                "user_instruction": user_input,
                "persona": persona,
                "complexity_level": complexity_level,
                "target_token_range": target_token_range,
                "input_token_count": input_token_count
            }).text
            result = chain.invoke({"chat_history": chat_history, "input": current_input})
        elif intent == "modify_prd":
            # Fixed list slicing syntax
            last_chat_history = chat_history[:-1] if len(chat_history) >= 4 else chat_history
            modification_history_messages = [
                    msg.content for msg in chat_history[-3:] if isinstance(msg, AIMessage)
                ]
            result = modification_chain.invoke({
                "extracted_text": text,
                "complexity_level": complexity_level,
                "user_instruction": user_input,
                "persona": persona,
                "recent_prd": last_chat_history,
                "target_token_range": target_token_range,
                "modification_history": modification_history_messages
            })
        # elif intent == "document_query":
        #     result = llm.invoke([HumanMessage(content=f"Based on the following extracted document:\n\n{text}\n\nAnswer the user's question:\n{user_input}")]).content
        elif intent == "document_query":
            result = document_query_chain.invoke({
                "extracted_text": text,
                "user_instruction": user_input
            })
        elif intent == "greeting":
            result = "Hello! I'm here to help you create and modify PRDs (Product Requirements Documents). How can I assist you today?"
        elif intent == "unknown":
            result = "I'm not sure how to help with that. Could you please clarify your request regarding the project document?"
        else:
            # Default fallback
            result = chain.invoke({
                "chat_history": chat_history,
                "input": f"Based on the following text, {user_input}:\n\n{text}"
            })

        return PrdUploadService.normalize_headings(result)

    @staticmethod
    async def generate_prd_output_stream(
        text: str,
        chat_history: List[BaseMessage],
        user_input: str,
        db: Session,
        session_id: str
    ) -> AsyncGenerator[str, None]:
        """
        Streaming version of generate_prd_output that yields chunks of the response
        """
        if not text:
            stored = (
                db.query(ExtractedFileText)
                .filter(ExtractedFileText.session_id == session_id)
                .order_by(ExtractedFileText.created_at.desc())
                .first()
            )
            text = stored.extracted_text if stored else ""

        chat_template = ChatPromptTemplate.from_messages([
            ("system", "You are an AI assistant for writing PRDs from extracted documents."),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}")
        ])

        # Use streaming LLM
        llm = ChatOpenAI(model="gpt-4o", streaming=True)
        input_token_count = PrdUploadService.count_tokens(text)
        complexity_level, target_token_range = PrdUploadService.classify_complexity_by_token_count(input_token_count)

        try:
            persona_prompt_template = load_prompt("prompt/persona_prompt.json")
            intent_prompt_template = load_prompt("prompt/intent_classifier_prompt.json")
            modification_prompt_template = load_prompt("prompt/modification_template.json")
            prompt_template = load_prompt("prompt/prd_generation_template.json")
            document_query_prompt_template = load_prompt("prompt/document_query_prompt.json")
        except FileNotFoundError as e:
            logger.error(f"Prompt file not found: {e}")
            chain = chat_template | llm
            if not user_input:
                user_input = "Generate a PRD"

            # Stream the response - yield raw content without normalization
            async for chunk in chain.astream({
                "chat_history": chat_history,
                "input": f"Based on the following text, generate a comprehensive PRD:\n\n{text}\n\nUser instruction: {user_input}"
            }):
                if hasattr(chunk, 'content'):
                    content = chunk.content
                    if content:
                        logger.info(f"LLM chunk (fallback): '{content}' (repr: {repr(content)})")
                        yield content  # Yield raw content to preserve spacing
            return

        # Non-streaming chains for classification (SAME AS ORIGINAL)
        persona_chain = persona_prompt_template | ChatOpenAI(model="gpt-4o") | StrOutputParser()
        intent_chain = intent_prompt_template | ChatOpenAI(model="gpt-4o") | StrOutputParser()
        modification_chain = modification_prompt_template | ChatOpenAI(model="gpt-4o") | StrOutputParser()
        document_query_chain = document_query_prompt_template | ChatOpenAI(model="gpt-4o") | StrOutputParser()

        persona = persona_chain.invoke({"extracted_text": text})

        # Streaming chain for main response (SAME AS ORIGINAL)
        chain = chat_template | llm | StrOutputParser()

        if not user_input:
            user_input = "Generate a PRD"
        intent = intent_chain.invoke({"query": user_input})

        # Debug logging for intent classification
        logger.info(f"Intent Classification Debug:")
        logger.info(f"  User Input: '{user_input}'")
        logger.info(f"  Classified Intent: '{intent}'")
        logger.info(f"  Persona: '{persona[:100]}...' (truncated)")
        logger.info(f"  Chat History Length: {len(chat_history)}")

        if intent == "generate_prd":
            # EXACT SAME AS ORIGINAL - Use prompt_template.invoke().text
            current_input = prompt_template.invoke({
                "extracted_text": text,
                "user_instruction": user_input,
                "persona": persona,
                "complexity_level": complexity_level,
                "target_token_range": target_token_range,
                "input_token_count": input_token_count
            }).text

            logger.info(f"Generated PRD prompt: '{current_input[:200]}...'")

            # Stream the response using the same chain structure as original
            async for chunk in chain.astream({"chat_history": chat_history, "input": current_input}):
                if hasattr(chunk, 'content'):
                    content = chunk.content
                    if content:
                        logger.info(f"LLM chunk (generate_prd): '{content}' (repr: {repr(content)})")
                        yield content  # Yield raw content to preserve spacing

        elif intent == "modify_prd":
            # EXACT SAME AS ORIGINAL - Fixed list slicing syntax
            last_chat_history = chat_history[:-1] if len(chat_history) >= 4 else chat_history
            modification_history_messages = [
                msg.content for msg in chat_history[-3:] if isinstance(msg, AIMessage)
            ]

            logger.info(f"Modifying PRD with chat history length: {len(chat_history)}")
            logger.info(f"Modification history messages: {len(modification_history_messages)}")

            # Use the SAME modification_chain as original (with StrOutputParser)
            async for chunk in modification_chain.astream({
                "extracted_text": text,
                "complexity_level": complexity_level,
                "user_instruction": user_input,
                "persona": persona,
                "recent_prd": last_chat_history,
                "target_token_range": target_token_range,
                "modification_history": modification_history_messages
            }):
                if hasattr(chunk, 'content'):
                    content = chunk.content
                    if content:
                        logger.info(f"LLM chunk (modify_prd): '{content}' (repr: {repr(content)})")
                        yield content  # Yield raw content to preserve spacing

        elif intent == "document_query":
            # Use the SAME document_query_chain as original (with StrOutputParser)
            async for chunk in document_query_chain.astream({
                "extracted_text": text,
                "user_instruction": user_input
            }):
                if hasattr(chunk, 'content'):
                    content = chunk.content
                    if content:
                        logger.info(f"LLM chunk (document_query): '{content}' (repr: {repr(content)})")
                        yield content  # Yield raw content to preserve spacing

        elif intent == "greeting":
            yield "Hello! I'm here to help you create and modify PRDs (Product Requirements Documents). How can I assist you today?"

        elif intent == "unknown":
            yield "I'm not sure how to help with that. Could you please clarify your request regarding the project document?"

        else:
            # Default fallback - SAME AS ORIGINAL
            async for chunk in chain.astream({
                "chat_history": chat_history,
                "input": f"Based on the following text, {user_input}:\n\n{text}"
            }):
                if hasattr(chunk, 'content'):
                    content = chunk.content
                    if content:
                        logger.info(f"LLM chunk (fallback): '{content}' (repr: {repr(content)})")
                        yield content  # Yield raw content to preserve spacing

    @staticmethod
    def save_chat_to_db(db, session_id: str, chat_id: str, user_msg: str, ai_msg: str):
        now = datetime.now()
        db.add_all([
            ChatHistory(session_id=session_id, role="human", chat_id=chat_id, message=user_msg, created_at=now),
            ChatHistory(session_id=session_id, role="ai", chat_id=chat_id, message=ai_msg, created_at=now)
        ])
        db.commit()

    @staticmethod
    def process_uploaded_files(user_id: int, user_input: Optional[str], files: List[UploadFile], session_id: str, db: Session) -> dict:
        combined_text = ""
        failed_files = []
        uploaded_file_messages = []
        image_urls = []
        filenames = []
        chat_history = []
        chat_id = str(uuid4())
        chathistory = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).limit(16).all()
        print('asc++++++++++++++',chat_history,'++++++++++++++++++++++++++++++++++')
        chat_history = chat_history[::-1]
        print('rev+++++++++++++',chat_history,'+++++++++++++++++++++++')
        
        for record in chathistory:
            if record.role == "human":
                chat_history.append(HumanMessage(content=record.message))
            elif record.role == "ai":
                chat_history.append(AIMessage(content=record.message))
        if files:
            logger.info(f"Processing {len(files)} files")
            for file in files:
                try:
                    filename = file.filename
                    filenames.append(filename)
                    temp_path, content = PrdUploadService.save_temp_file(file)
                    if Path(filename).suffix.lower() in ['.jpg', '.jpeg', '.png']:
                        image_url = PrdUploadService.process_image(filename, content)
                        image_urls.append(image_url)
                        uploaded_file_messages.append(f"![{filename}]({image_url})")
                    else:
                        uploaded_file_messages.append(filename)
                    extracted_text = PrdUploadService.extract_and_format_text(temp_path, filename)
                    if extracted_text:
                        PrdUploadService.add_extracted_file_text(db, session_id, extracted_text)
                    else:
                        pass
                    if "Error processing video:" in extracted_text or "Error extracting text from" in extracted_text:
                        logger.warning(f"Failed to extract content from {filename}, adding to failed files")
                        failed_files.append({"filename": filename, "status": "failed", "error": "Content extraction failed"})
                    else:
                        combined_text += extracted_text
                except Exception as e:
                    logger.error(f"Failed to process file {filename}: {str(e)}")
                    failed_files.append({"filename": filename, "status": "failed", "error": str(e)})
                finally:
                    try:
                        os.unlink(temp_path)
                    except:
                        pass
                    file.file.seek(0)
        if not combined_text.strip() and files:
            prd_result = "Unable to extract content from the uploaded files. Please ensure the files are not corrupted and contain readable content. For video files, ensure they contain audio that can be transcribed."
        else:
            prd_result = PrdUploadService.generate_prd_output(combined_text.strip(), chat_history, user_input,db=db, session_id=session_id)        
        if uploaded_file_messages and user_input:
            user_msg = "Uploaded files:\n" + "\n".join(uploaded_file_messages) + "\n\n" + user_input
        elif uploaded_file_messages:
            user_msg = "Uploaded files:\n" + "\n".join(uploaded_file_messages)
        else:
            user_msg = user_input
        PrdUploadService.save_chat_to_db(db, session_id, chat_id, user_msg, prd_result)        
        return {
            "session_id": session_id,
            "prd_result": prd_result,
            "filenames": filenames,
            "user_input": user_input,
            "image_urls": image_urls,
            "failed_files": failed_files
        }

    @staticmethod
    def add_extracted_file_text(db: Session, session_id: str, extracted_text: str):
        if not extracted_text:
            return
        new_entry = ExtractedFileText(
            session_id=session_id,
            extracted_text=extracted_text,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(new_entry)
        db.commit()
        db.refresh(new_entry)
        return new_entry

    @staticmethod
    async def process_uploaded_files_stream(
        user_id: int,
        user_input: Optional[str],
        files: List[UploadFile],
        file_contents: List[Dict[str, Any]],
        session_id: str,
        db: Session
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Streaming version of process_uploaded_files that yields progress updates
        """
        combined_text = ""
        failed_files = []
        uploaded_file_messages = []
        image_urls = []
        filenames = []
        chat_history = []
        chat_id = str(uuid4())

        # Load chat history
        yield {
            'type': 'progress',
            'stage': 'chat_history',
            'message': 'Loading chat history...'
        }
        await asyncio.sleep(0.1)

        chathistory = db.query(ChatHistory).filter(
            ChatHistory.session_id == session_id
        ).order_by(ChatHistory.created_at.asc()).limit(16).all()

        chat_history = chat_history[::-1]

        for record in chathistory:
            if record.role == "human":
                chat_history.append(HumanMessage(content=record.message))
            elif record.role == "ai":
                chat_history.append(AIMessage(content=record.message))

        # Process files if any
        if file_contents:
            total_files = len(file_contents)
            yield {
                'type': 'progress',
                'stage': 'file_processing',
                'message': f'Processing {total_files} files...',
                'total_files': total_files,
                'current_file': 0
            }
            await asyncio.sleep(0.1)

            logger.info(f"Processing {len(file_contents)} files")
            for i, file_data in enumerate(file_contents):
                temp_path = None
                try:
                    filename = file_data['filename']
                    content = file_data.get('content')
                    file_error = file_data.get('error')

                    filenames.append(filename)

                    yield {
                        'type': 'progress',
                        'stage': 'file_processing',
                        'message': f'Processing file: {filename}',
                        'total_files': total_files,
                        'current_file': i + 1,
                        'filename': filename
                    }
                    await asyncio.sleep(0.1)

                    # Check if there was an error reading the file
                    if file_error or content is None:
                        error_msg = file_error or "File content is empty"
                        logger.error(f"Cannot process file {filename}: {error_msg}")
                        failed_files.append({"filename": filename, "status": "failed", "error": error_msg})
                        continue

                    # Create temporary file
                    suffix = Path(filename).suffix
                    import tempfile
                    with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
                        temp_file.write(content)
                        temp_file.flush()
                        temp_path = Path(temp_file.name)

                    if Path(filename).suffix.lower() in ['.jpg', '.jpeg', '.png']:
                        image_url = PrdUploadService.process_image(filename, content)
                        image_urls.append(image_url)
                        uploaded_file_messages.append(f"![{filename}]({image_url})")
                    else:
                        uploaded_file_messages.append(filename)

                    yield {
                        'type': 'progress',
                        'stage': 'text_extraction',
                        'message': f'Extracting text from: {filename}',
                        'total_files': total_files,
                        'current_file': i + 1,
                        'filename': filename
                    }
                    await asyncio.sleep(0.1)

                    extracted_text = PrdUploadService.extract_and_format_text(temp_path, filename)
                    if extracted_text:
                        PrdUploadService.add_extracted_file_text(db, session_id, extracted_text)

                    if "Error processing video:" in extracted_text or "Error extracting text from" in extracted_text:
                        logger.warning(f"Failed to extract content from {filename}, adding to failed files")
                        failed_files.append({"filename": filename, "status": "failed", "error": "Content extraction failed"})
                    else:
                        combined_text += extracted_text

                except Exception as e:
                    logger.error(f"Failed to process file {filename if 'filename' in locals() else 'unknown'}: {str(e)}")
                    failed_files.append({"filename": filename if 'filename' in locals() else 'unknown', "status": "failed", "error": str(e)})
                finally:
                    # Clean up temporary file
                    if temp_path and os.path.exists(temp_path):
                        try:
                            os.unlink(temp_path)
                        except Exception as cleanup_error:
                            logger.warning(f"Failed to cleanup temp file {temp_path}: {cleanup_error}")

        # Generate PRD with streaming
        yield {
            'type': 'progress',
            'stage': 'prd_generation',
            'message': 'Starting PRD generation...'
        }
        await asyncio.sleep(0.1)

        if not combined_text.strip() and files:
            prd_result = "Unable to extract content from the uploaded files. Please ensure the files are not corrupted and contain readable content. For video files, ensure they contain audio that can be transcribed."
            yield {
                'type': 'prd_chunk',
                'content': prd_result,
                'is_final': True
            }
        else:
            # Stream PRD generation with word boundary handling
            full_prd_result = ""
            word_buffer = ""  # Buffer to handle incomplete words

            async for chunk in PrdUploadService.generate_prd_output_stream(
                combined_text.strip(),
                chat_history,
                user_input,
                db=db,
                session_id=session_id
            ):
                if chunk:
                    # Add debug logging to see what chunks we're getting
                    logger.info(f"Raw chunk: '{chunk}' (length: {len(chunk)}) (repr: {repr(chunk)})")

                    # Add chunk to buffer
                    word_buffer += chunk

                    # Process complete words from buffer
                    processed_content = ""

                    # If buffer ends with a space or punctuation, it's likely a complete word/sentence
                    if word_buffer.endswith((' ', '\n', '.', ',', '!', '?', ':', ';', ')', ']', '}', '"', "'")):
                        processed_content = word_buffer
                        word_buffer = ""
                    # If buffer has spaces in the middle, extract complete words
                    elif ' ' in word_buffer:
                        parts = word_buffer.rsplit(' ', 1)  # Split from the right, keep last part
                        if len(parts) == 2:
                            processed_content = parts[0] + ' '  # Include the space
                            word_buffer = parts[1]  # Keep the incomplete word
                        else:
                            processed_content = word_buffer
                            word_buffer = ""
                    # For very long buffers (>50 chars), flush to avoid memory issues
                    elif len(word_buffer) > 50:
                        processed_content = word_buffer
                        word_buffer = ""

                    if processed_content:
                        logger.info(f"Processed content: '{processed_content}' (repr: {repr(processed_content)})")
                        full_prd_result += processed_content

                        yield {
                            'type': 'prd_chunk',
                            'content': processed_content,
                            'is_final': False
                        }
                    else:
                        logger.info(f"Buffering incomplete word: '{word_buffer}'")

                    await asyncio.sleep(0.01)

            # Flush any remaining content in buffer
            if word_buffer:
                logger.info(f"Flushing remaining buffer: '{word_buffer}'")
                full_prd_result += word_buffer
                yield {
                    'type': 'prd_chunk',
                    'content': word_buffer,
                    'is_final': False
                }

            # Apply normalization to the complete PRD at the end
            prd_result = PrdUploadService.normalize_headings(full_prd_result)

            # Log the final result to debug spacing issues
            logger.info(f"Final PRD result length: {len(prd_result)}")
            logger.info(f"Final PRD first 200 chars: '{prd_result[:200]}'")

            yield {
                'type': 'prd_chunk',
                'content': '',
                'is_final': True
            }

        # Save chat to database
        yield {
            'type': 'progress',
            'stage': 'saving_chat',
            'message': 'Saving chat to database...'
        }
        await asyncio.sleep(0.1)

        # Ensure user_msg is never None
        if uploaded_file_messages and user_input:
            user_msg = "Uploaded files:\n" + "\n".join(uploaded_file_messages) + "\n\n" + user_input
        elif uploaded_file_messages:
            user_msg = "Uploaded files:\n" + "\n".join(uploaded_file_messages)
        elif user_input:
            user_msg = user_input
        else:
            user_msg = "File processing request"  # Default message if everything is None

        PrdUploadService.save_chat_to_db(db, session_id, chat_id, user_msg, prd_result)

        # Final result
        yield {
            'type': 'result',
            'data': {
                "session_id": session_id,
                "prd_result": prd_result,
                "filenames": filenames,
                "user_input": user_input,
                "image_urls": image_urls,
                "failed_files": failed_files
            }
        }
