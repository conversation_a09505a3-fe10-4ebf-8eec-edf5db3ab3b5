"""add column in user

Revision ID: 32ada33127e4
Revises: c302119c1a79
Create Date: 2025-07-02 14:16:29.152177

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '32ada33127e4'
down_revision: Union[str, None] = 'c302119c1a79'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.<PERSON>umn('is_deleted', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'is_deleted')
    # ### end Alembic commands ###
