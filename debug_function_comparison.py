#!/usr/bin/env python3
"""
Debug script to compare inputs and outputs between streaming and non-streaming functions
"""

import requests
import json
import time

def test_non_streaming_endpoint():
    """
    Test the original non-streaming endpoint
    """
    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/api/v1/prd_upload/upload_files"
    
    files = {
        'files': ('test.txt', 'This is a test document for AI Multilingual Voice Assistant PRD generation.', 'text/plain')
    }
    
    test_cases = [
        {
            "name": "Generate PRD",
            "user_input": "Generate a comprehensive PRD for AI Multilingual Voice Assistant"
        },
        {
            "name": "Document Query", 
            "user_input": "What are the main features mentioned in the document?"
        },
        {
            "name": "Greeting",
            "user_input": "Hello"
        }
    ]
    
    print("🔍 Testing NON-STREAMING endpoint")
    print("=" * 60)
    
    results = {}
    
    for test_case in test_cases:
        print(f"\n📋 Test Case: {test_case['name']}")
        print(f"Input: '{test_case['user_input']}'")
        print("-" * 40)
        
        try:
            data = {
                'user_input': test_case['user_input'],
                'session_id': None
            }
            
            response = requests.post(endpoint, files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                prd_result = result.get('data', {}).get('prd_result', '')
                
                print(f"✅ Status: {response.status_code}")
                print(f"📄 Result length: {len(prd_result)}")
                print(f"📝 First 200 chars: '{prd_result[:200]}...'")
                
                results[test_case['name']] = {
                    'input': test_case['user_input'],
                    'output': prd_result,
                    'length': len(prd_result),
                    'status': 'success'
                }
            else:
                print(f"❌ Status: {response.status_code}")
                print(f"Error: {response.text}")
                results[test_case['name']] = {
                    'input': test_case['user_input'],
                    'status': 'error',
                    'error': response.text
                }
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results[test_case['name']] = {
                'input': test_case['user_input'],
                'status': 'exception',
                'error': str(e)
            }
        
        time.sleep(1)  # Small delay between requests
    
    return results

def test_streaming_endpoint():
    """
    Test the streaming endpoint
    """
    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/api/v1/prd_upload/upload_files_stream"
    
    files = {
        'files': ('test.txt', 'This is a test document for AI Multilingual Voice Assistant PRD generation.', 'text/plain')
    }
    
    test_cases = [
        {
            "name": "Generate PRD",
            "user_input": "Generate a comprehensive PRD for AI Multilingual Voice Assistant"
        },
        {
            "name": "Document Query",
            "user_input": "What are the main features mentioned in the document?"
        },
        {
            "name": "Greeting",
            "user_input": "Hello"
        }
    ]
    
    print("\n\n🔍 Testing STREAMING endpoint")
    print("=" * 60)
    
    results = {}
    
    for test_case in test_cases:
        print(f"\n📋 Test Case: {test_case['name']}")
        print(f"Input: '{test_case['user_input']}'")
        print("-" * 40)
        
        try:
            data = {
                'user_input': test_case['user_input'],
                'session_id': None
            }
            
            response = requests.post(
                endpoint,
                files=files,
                data=data,
                stream=True,
                headers={'Accept': 'text/event-stream'}
            )
            
            if response.status_code == 200:
                full_content = ""
                chunk_count = 0
                
                for line in response.iter_lines(decode_unicode=True):
                    if line and line.startswith('data: '):
                        try:
                            json_data = line[6:]
                            data_obj = json.loads(json_data)
                            
                            if data_obj.get('type') == 'prd_chunk':
                                content = data_obj.get('content', '')
                                is_final = data_obj.get('is_final', False)
                                
                                if content and not is_final:
                                    full_content += content
                                    chunk_count += 1
                                elif is_final:
                                    break
                            
                            elif data_obj.get('type') == 'result':
                                final_result = data_obj.get('data', {}).get('prd_result', '')
                                if final_result:
                                    full_content = final_result
                                break
                        
                        except json.JSONDecodeError:
                            continue
                
                print(f"✅ Status: {response.status_code}")
                print(f"📦 Chunks received: {chunk_count}")
                print(f"📄 Result length: {len(full_content)}")
                print(f"📝 First 200 chars: '{full_content[:200]}...'")
                
                results[test_case['name']] = {
                    'input': test_case['user_input'],
                    'output': full_content,
                    'length': len(full_content),
                    'chunks': chunk_count,
                    'status': 'success'
                }
            else:
                print(f"❌ Status: {response.status_code}")
                print(f"Error: {response.text}")
                results[test_case['name']] = {
                    'input': test_case['user_input'],
                    'status': 'error',
                    'error': response.text
                }
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results[test_case['name']] = {
                'input': test_case['user_input'],
                'status': 'exception',
                'error': str(e)
            }
        
        time.sleep(1)  # Small delay between requests
    
    return results

def compare_results(non_streaming_results, streaming_results):
    """
    Compare the results from both endpoints
    """
    print("\n\n🔍 COMPARISON ANALYSIS")
    print("=" * 60)
    
    for test_name in non_streaming_results.keys():
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        ns_result = non_streaming_results.get(test_name, {})
        s_result = streaming_results.get(test_name, {})
        
        # Compare status
        ns_status = ns_result.get('status', 'unknown')
        s_status = s_result.get('status', 'unknown')
        
        print(f"Status - Non-streaming: {ns_status}, Streaming: {s_status}")
        
        if ns_status == 'success' and s_status == 'success':
            ns_output = ns_result.get('output', '')
            s_output = s_result.get('output', '')
            ns_length = len(ns_output)
            s_length = len(s_output)
            
            print(f"Length - Non-streaming: {ns_length}, Streaming: {s_length}")
            
            # Check if outputs are similar
            if ns_output == s_output:
                print("✅ Outputs are IDENTICAL")
            else:
                print("❌ Outputs are DIFFERENT")
                
                # Show first difference
                min_len = min(len(ns_output), len(s_output))
                for i in range(min_len):
                    if ns_output[i] != s_output[i]:
                        print(f"First difference at position {i}:")
                        print(f"  Non-streaming: '{ns_output[max(0, i-10):i+10]}'")
                        print(f"  Streaming:     '{s_output[max(0, i-10):i+10]}'")
                        break
                
                # Show length difference
                if ns_length != s_length:
                    print(f"Length difference: {abs(ns_length - s_length)} characters")
        
        elif ns_status != 'success' or s_status != 'success':
            print(f"❌ One or both endpoints failed")
            if ns_status != 'success':
                print(f"  Non-streaming error: {ns_result.get('error', 'Unknown')}")
            if s_status != 'success':
                print(f"  Streaming error: {s_result.get('error', 'Unknown')}")

def main():
    """
    Main function to run all tests
    """
    print("🧪 Function Comparison Debug Tool")
    print("=" * 60)
    print("This tool will test both endpoints with the same inputs")
    print("and compare their outputs to identify differences.")
    print()
    
    # Test both endpoints
    non_streaming_results = test_non_streaming_endpoint()
    streaming_results = test_streaming_endpoint()
    
    # Compare results
    compare_results(non_streaming_results, streaming_results)
    
    print("\n" + "=" * 60)
    print("🎯 DEBUGGING TIPS:")
    print("=" * 60)
    print("1. Check server logs for intent classification debug output")
    print("2. Look for differences in prompt template usage")
    print("3. Verify chain construction (StrOutputParser vs raw LLM)")
    print("4. Compare input parameters passed to each chain")
    print("5. Check if normalization is applied consistently")

if __name__ == "__main__":
    main()
