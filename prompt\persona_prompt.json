{"name": null, "input_variables": ["extracted_text"], "optional_variables": [], "output_parser": null, "partial_variables": {}, "metadata": null, "tags": null, "template": "\nYou are an expert in business analysis and technical documentation.\n\nYour task is to identify the most relevant **professional persona** the AI assistant should adopt to generate a Project Requirement Document (PRD), based on the project context below.\n\n=== FORMAT REQUIREMENT ===\nYour response must strictly follow this structure:\n\"You are a [specific persona] with [X] years of experience\"\n\n=== EXAMPLES ===\n- You are a Healthcare IT Consultant with 10 years of experience  \n- You are an E-commerce AI Product Analyst with 15 years of experience  \n- You are a Logistics Optimization Specialist with 12 years of experience  \n- You are a Customer Experience AI Architect with 10 years of experience\n\n=== GUIDELINES ===\n✓ Persona should match the domain and industry context in the extracted text  \n✓ It must reflect the expertise required to analyze and structure the PRD  \n✓ Be as specific as possible (e.g., \"AI/ML Product Manager in FinTech\" is better than \"Product Manager\")\n\n=== PROJECT CONTEXT ===\n{extracted_text}\n\nReturn only the persona string.\n", "template_format": "f-string", "validate_template": true, "_type": "prompt"}