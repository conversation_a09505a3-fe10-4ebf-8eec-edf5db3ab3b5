"""add foreignkey

Revision ID: d665d7edd607
Revises: 094418da8633
Create Date: 2025-06-10 15:44:07.772878

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd665d7edd607'
down_revision: Union[str, None] = '094418da8633'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('notion_tokens', sa.Column('user_id', sa.Integer(), nullable=False))
    op.create_foreign_key(None, 'notion_tokens', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'notion_tokens', type_='foreignkey')
    op.drop_column('notion_tokens', 'user_id')
    # ### end Alembic commands ###
