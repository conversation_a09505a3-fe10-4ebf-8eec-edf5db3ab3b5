"""addcolumninpaymnettable

Revision ID: 0ed14f0d0c64
Revises: 3e35ab37efec
Create Date: 2025-05-01 11:07:20.931756

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0ed14f0d0c64'
down_revision: Union[str, None] = '3e35ab37efec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('payments', sa.Column('reminder_date', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('payments', 'reminder_date')
    # ### end Alembic commands ###
