from sqlalchemy import Column, Integer, String, DateTime, Text
from app.database.base import Base
from datetime import datetime

class ExtractedFileText(Base):
    __tablename__ = "extracted_file_texts"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, index=True, nullable=False)
    extracted_text = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
