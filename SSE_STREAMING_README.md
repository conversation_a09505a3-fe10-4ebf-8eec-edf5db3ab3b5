# SSE Streaming Implementation for PRD Upload

This document explains the Server-Sent Events (SSE) streaming implementation for the PRD upload functionality.

## Overview

The SSE streaming implementation provides real-time progress updates during file processing and PRD generation. Instead of waiting for the entire process to complete, users receive live updates about each stage of the process.

## New Endpoints

### `/upload_files_stream` (POST)
A new streaming endpoint that provides real-time progress updates via Server-Sent Events.

**Parameters:**
- `files`: List of files to upload (same as original endpoint)
- `user_input`: Optional user instructions (same as original endpoint)
- `session_id`: Optional session ID (same as original endpoint)

**Response:** Server-Sent Events stream with `Content-Type: text/event-stream`

## Stream Message Types

The streaming endpoint sends different types of messages:

### 1. Progress Messages
```json
{
  "type": "progress",
  "stage": "validation|session|processing|file_processing|text_extraction|prd_generation|saving_chat",
  "message": "Human-readable progress message",
  "total_files": 3,
  "current_file": 1,
  "filename": "document.pdf"
}
```

### 2. PRD Content Chunks
```json
{
  "type": "prd_chunk",
  "content": "Partial PRD content...",
  "is_final": false
}
```

### 3. Final Result
```json
{
  "type": "result",
  "data": {
    "session_id": "uuid",
    "prd_result": "Complete PRD content",
    "filenames": ["file1.pdf", "file2.txt"],
    "user_input": "User instructions",
    "image_urls": [],
    "failed_files": []
  }
}
```

### 4. Completion Message
```json
{
  "type": "complete",
  "message": "Processing completed successfully"
}
```

### 5. Error Messages
```json
{
  "type": "error",
  "message": "Error description"
}
```

## Processing Stages

The streaming implementation provides updates for these stages:

1. **Validation** - User permission checks
2. **Session** - Session setup and validation
3. **File Processing** - Individual file handling
4. **Text Extraction** - Content extraction from files
5. **PRD Generation** - AI-powered PRD creation (streamed)
6. **Saving Chat** - Storing conversation in database

## Implementation Details

### Backend Changes

1. **New Route**: Added `/upload_files_stream` endpoint in `prd_upload_routes.py`
2. **Streaming Service**: Created `process_uploaded_files_stream()` in `PrdUploadService`
3. **Streaming PRD Generation**: Added `generate_prd_output_stream()` for real-time AI response
4. **Progress Tracking**: Integrated progress updates throughout the processing pipeline

### Key Features

- **Real-time Progress**: Live updates for each processing stage
- **File-by-file Tracking**: Individual progress for multiple files
- **Streaming AI Response**: PRD content streams as it's generated
- **Error Handling**: Graceful error reporting via SSE
- **Chat Persistence**: Full response saved to database after streaming

## Usage Examples

### JavaScript/Browser
```javascript
const formData = new FormData();
formData.append('files', fileInput.files[0]);
formData.append('user_input', 'Generate a comprehensive PRD');

fetch('/api/v1/prd_upload/upload_files_stream', {
    method: 'POST',
    body: formData
})
.then(response => {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    function readStream() {
        return reader.read().then(({ done, value }) => {
            if (done) return;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = JSON.parse(line.substring(6));
                    handleStreamData(data);
                }
            }
            
            return readStream();
        });
    }
    
    return readStream();
});
```

### Python
```python
import requests
import json

response = requests.post(
    'http://localhost:8000/api/v1/prd_upload/upload_files_stream',
    files={'files': ('test.txt', 'content', 'text/plain')},
    data={'user_input': 'Generate PRD'},
    stream=True
)

for line in response.iter_lines(decode_unicode=True):
    if line and line.startswith('data: '):
        data = json.loads(line[6:])
        print(f"Received: {data}")
```

### cURL
```bash
curl -X POST "http://localhost:8000/api/v1/prd_upload/upload_files_stream" \
  -H "Accept: text/event-stream" \
  -F "files=@document.pdf" \
  -F "user_input=Generate a comprehensive PRD"
```

## Testing

### Test Files Included

1. **`test_sse_streaming.py`** - Python script to test the streaming endpoint
2. **`sse_client_demo.html`** - HTML client with real-time UI updates

### Running Tests

1. Start your FastAPI server
2. Run the Python test:
   ```bash
   python test_sse_streaming.py
   ```
3. Open `sse_client_demo.html` in a browser for interactive testing

## Benefits

1. **Better User Experience** - Real-time feedback instead of waiting
2. **Progress Visibility** - Users see exactly what's happening
3. **Error Transparency** - Immediate error reporting
4. **Scalability** - Non-blocking processing for large files
5. **Debugging** - Detailed progress logs for troubleshooting

## Backward Compatibility

The original `/upload_files` endpoint remains unchanged, ensuring existing integrations continue to work. The new streaming endpoint is an additional feature.

## Performance Considerations

- Small delays (0.01-0.1s) between chunks prevent overwhelming clients
- Streaming reduces memory usage for large responses
- Database operations remain atomic and consistent
- Error handling ensures graceful degradation

## Security

- Same authentication and validation as original endpoint
- Rate limiting applies to streaming endpoint
- File size and type restrictions maintained
- Session management unchanged

## Future Enhancements

Potential improvements for the streaming implementation:

1. **Progress Percentages** - More granular progress tracking
2. **Cancellation Support** - Ability to cancel long-running operations
3. **Retry Mechanisms** - Automatic retry for failed chunks
4. **Compression** - Gzip compression for large streams
5. **WebSocket Alternative** - Bidirectional communication option
