#!/usr/bin/env python3
"""
Test script to demonstrate SSE streaming functionality for the upload_files endpoint.
This script shows how to consume the Server-Sent Events from the streaming endpoint.
"""

import requests
import json
import time

def test_sse_streaming():
    """
    Test the SSE streaming endpoint
    """
    # Replace with your actual server URL
    base_url = "http://localhost:8000"  # Adjust port as needed
    endpoint = f"{base_url}/api/v1/prd_upload/upload_files_stream"
    
    # Prepare test data
    files = {
        'files': ('test.txt', 'This is a test document for PRD generation.', 'text/plain')
    }
    
    data = {
        'user_input': 'Generate a comprehensive PRD from this document',
        'session_id': None  # Will create a new session
    }
    
    print("🚀 Starting SSE streaming test...")
    print(f"📡 Connecting to: {endpoint}")
    print("-" * 50)
    
    try:
        # Make the streaming request
        response = requests.post(
            endpoint,
            files=files,
            data=data,
            stream=True,
            headers={
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            }
        )
        
        if response.status_code != 200:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return
        
        print("✅ Connected successfully!")
        print("📥 Receiving streaming data...\n")
        
        # Process the streaming response
        for line in response.iter_lines(decode_unicode=True):
            if line:
                # SSE format: "data: {json_data}"
                if line.startswith('data: '):
                    try:
                        json_data = line[6:]  # Remove "data: " prefix
                        data = json.loads(json_data)
                        
                        # Handle different types of messages
                        if data.get('type') == 'progress':
                            stage = data.get('stage', 'unknown')
                            message = data.get('message', '')
                            print(f"🔄 [{stage.upper()}] {message}")
                            
                            # Show file processing progress
                            if 'total_files' in data and 'current_file' in data:
                                progress = f"({data['current_file']}/{data['total_files']})"
                                print(f"   📁 File progress: {progress}")
                        
                        elif data.get('type') == 'prd_chunk':
                            content = data.get('content', '')
                            is_final = data.get('is_final', False)
                            
                            if content:
                                print(f"📝 PRD Content: {content[:100]}{'...' if len(content) > 100 else ''}")
                            
                            if is_final:
                                print("✅ PRD generation completed!")
                        
                        elif data.get('type') == 'result':
                            result_data = data.get('data', {})
                            session_id = result_data.get('session_id')
                            filenames = result_data.get('filenames', [])
                            failed_files = result_data.get('failed_files', [])
                            
                            print("\n🎉 Final Results:")
                            print(f"   📋 Session ID: {session_id}")
                            print(f"   📄 Processed files: {', '.join(filenames) if filenames else 'None'}")
                            
                            if failed_files:
                                print(f"   ❌ Failed files: {len(failed_files)}")
                                for failed in failed_files:
                                    print(f"      - {failed.get('filename')}: {failed.get('error')}")
                        
                        elif data.get('type') == 'complete':
                            print(f"✅ {data.get('message', 'Processing completed')}")
                            break
                        
                        elif data.get('type') == 'error':
                            print(f"❌ Error: {data.get('message', 'Unknown error')}")
                            break
                        
                        else:
                            print(f"📨 Unknown message type: {data}")
                    
                    except json.JSONDecodeError as e:
                        print(f"⚠️  JSON decode error: {e}")
                        print(f"   Raw line: {line}")
                
                # Small delay to make output readable
                time.sleep(0.1)
    
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 SSE streaming test completed!")

def test_with_curl():
    """
    Show how to test with curl command
    """
    print("\n📋 To test with curl, use this command:")
    print("-" * 40)
    curl_command = '''curl -X POST "http://localhost:8000/api/v1/prd_upload/upload_files_stream" \\
  -H "Accept: text/event-stream" \\
  -H "Cache-Control: no-cache" \\
  -F "files=@test.txt" \\
  -F "user_input=Generate a comprehensive PRD from this document"'''
    print(curl_command)
    print("-" * 40)

if __name__ == "__main__":
    print("🧪 SSE Streaming Test for PRD Upload")
    print("=" * 50)
    
    # Run the test
    test_sse_streaming()
    
    # Show curl example
    test_with_curl()
