"""add responsecolumin auditlogs

Revision ID: 65acfe0b762a
Revises: de6961a479d5
Create Date: 2025-06-24 11:39:34.467821

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '65acfe0b762a'
down_revision: Union[str, None] = 'de6961a479d5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('audit_logs', sa.Column('response', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('audit_logs', 'response')
    # ### end Alembic commands ###
