"""add api key setting

Revision ID: eec344f01994
Revises: 7c01ff314696
Create Date: 2025-07-02 11:42:05.831833

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'eec344f01994'
down_revision: Union[str, None] = '7c01ff314696'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('api_keys_settings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('key_name', sa.Enum('STRIPE_KEYS', 'OPENAI_API_KEYS', 'GOOGLE_OAUTH_KEYS', 'GITHUB_OAUTH_KEYS', 'NOTION_API_KEYS', 'ATLASSIAN_API_KEYS', 'SMTP_KEYS', name='keyenum'), nullable=False),
    sa.Column('key_id', postgresql.JSON(astext_type=sa.Text()), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_api_keys_settings_id'), 'api_keys_settings', ['id'], unique=False)
    op.create_index(op.f('ix_api_keys_settings_key_name'), 'api_keys_settings', ['key_name'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_api_keys_settings_key_name'), table_name='api_keys_settings')
    op.drop_index(op.f('ix_api_keys_settings_id'), table_name='api_keys_settings')
    op.drop_table('api_keys_settings')
    # ### end Alembic commands ###
