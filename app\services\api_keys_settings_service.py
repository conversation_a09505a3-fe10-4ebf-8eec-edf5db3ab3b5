from sqlalchemy.ext.asyncio import AsyncSession
from app.database.models.api_keys_settings_model import APIKeysSettings
from app.schemas.api_keys_setting_schema import APIKeyCreate, APIKeyUpdate
from sqlalchemy.future import select
from typing import Optional, List
from app.database.enum import KeyEnum
from cryptography.fernet import <PERSON>rnet
from app.core.config import settings
import json
from typing import Union
from fastapi import HTTPException



fernet = Fernet(settings.FERNET_KEY)

class APIKEYSETTINGService():
    @staticmethod
    def decrypt_api_key(encrypted_json: Union[str, dict]) -> str:
        if isinstance(encrypted_json, str):
            encrypted_dict = json.loads(encrypted_json)
        elif isinstance(encrypted_json, dict):
            encrypted_dict = encrypted_json
        else:
            raise ValueError("Invalid type for encrypted_json")
        
        decrypted_dict = {key: fernet.decrypt(value.encode()).decode() for key, value in encrypted_dict.items()}
        
        return decrypted_dict

    
    @staticmethod
    def encrypt_key_values(data: dict) -> dict:
        return {
            key: fernet.encrypt(value.encode()).decode()
            for key, value in data.items()
        }
    
    @staticmethod
    async def create_api_key(db: AsyncSession, api_key_data: APIKeyCreate) -> APIKeysSettings:
        existing_key = db.execute(select(APIKeysSettings).where(APIKeysSettings.key_name == api_key_data.key_name))
        if existing_key.scalars().first():
            raise HTTPException(status_code=203, detail="API Key with this name already exists.")
        encrypted_dict = APIKEYSETTINGService.encrypt_key_values(api_key_data.key_id)

        new_key = APIKeysSettings(
            key_name=api_key_data.key_name,
            key_id=encrypted_dict
        )
        db.add(new_key)
        db.commit()
        db.refresh(new_key)
        return new_key
    
    @staticmethod
    async def get_api_key(db: AsyncSession, key_id: int) -> Optional[APIKeysSettings]:
        result = db.execute(select(APIKeysSettings).where(APIKeysSettings.id == key_id))
        keys = result.scalars().first()
        if not keys:
            raise HTTPException(status_code=203, detail="API Keys not found")
        return keys
        
    

    @staticmethod
    async def get_all_api_keys(db: AsyncSession) -> List[APIKeysSettings]:
        result = db.execute(select(APIKeysSettings).order_by(APIKeysSettings.id))
        keys = result.scalars().all()
        if not keys:
            raise HTTPException(status_code=203, detail="API Keys not found")
        
        for key in keys:
            key.key_id = APIKEYSETTINGService.decrypt_api_key(key.key_id)
        
        return keys


    @staticmethod
    async def update_api_key(db: AsyncSession, api_key_data: APIKeyUpdate) -> Optional[APIKeysSettings]:
        result = db.execute(select(APIKeysSettings).where(APIKeysSettings.id == api_key_data.id))
        api_key = result.scalars().first()
        if not api_key:
            raise HTTPException(status_code=203, detail="API Keys not found")
        encrypted_dict = APIKEYSETTINGService.encrypt_key_values(api_key_data.key_id)
        api_key.key_name = api_key_data.key_name
        api_key.key_id = encrypted_dict  
        db.commit()
        db.refresh(api_key)
        return api_key


    @staticmethod
    def get_api_key_by_name(db: AsyncSession, key_name: KeyEnum) -> Optional[APIKeysSettings]:
        result =  db.execute(select(APIKeysSettings).where(APIKeysSettings.key_name == key_name))
        keys = result.scalars().first()
        if not keys:
            return None
        decrypted_key_data  = APIKEYSETTINGService.decrypt_api_key(keys.key_id)
        return decrypted_key_data 

    