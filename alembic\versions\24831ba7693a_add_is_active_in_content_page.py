"""Add is active in content page

Revision ID: 24831ba7693a
Revises: aa14f5a2a069
Create Date: 2025-06-27 11:07:53.077877

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '24831ba7693a'
down_revision: Union[str, None] = 'aa14f5a2a069'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('content_pages', sa.Column('is_active', sa.<PERSON>(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('content_pages', 'is_active')
    # ### end Alembic commands ###
