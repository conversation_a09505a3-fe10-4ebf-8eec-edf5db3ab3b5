<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE Streaming Demo - PRD Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .upload-form {
            margin-bottom: 20px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
        }
        .progress-section {
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .log-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-progress { background-color: #e3f2fd; }
        .log-prd { background-color: #f3e5f5; }
        .log-error { background-color: #ffebee; color: #c62828; }
        .log-success { background-color: #e8f5e8; color: #2e7d32; }
        .prd-output {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
            min-height: 200px;
            white-space: pre-wrap;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        input[type="file"], input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.complete { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 SSE Streaming Demo - PRD Upload</h1>
        
        <div class="upload-form">
            <h3>Upload Files for PRD Generation</h3>
            <form id="uploadForm">
                <div>
                    <label for="files">Select Files:</label>
                    <input type="file" id="files" name="files" multiple accept=".txt,.pdf,.docx,.jpg,.jpeg,.png,.mp3,.wav,.mp4,.avi">
                </div>
                <div>
                    <label for="userInput">User Input (optional):</label>
                    <textarea id="userInput" name="userInput" rows="3" placeholder="Enter your instructions for PRD generation..."></textarea>
                </div>
                <div>
                    <label for="sessionId">Session ID (optional):</label>
                    <input type="text" id="sessionId" name="sessionId" placeholder="Leave empty to create new session">
                </div>
                <div>
                    <label for="serverUrl">Server URL:</label>
                    <input type="text" id="serverUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
                </div>
                <button type="submit" id="uploadBtn">Start Streaming Upload</button>
            </form>
        </div>

        <div id="statusDiv" class="status" style="display: none;"></div>

        <div class="progress-section">
            <h3>Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">Ready to upload...</div>
        </div>

        <div>
            <h3>Activity Log</h3>
            <div class="log-container" id="logContainer"></div>
        </div>

        <div>
            <h3>PRD Output</h3>
            <div class="prd-output" id="prdOutput">PRD content will appear here as it's generated...</div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let prdContent = '';

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('statusDiv');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function updateProgress(current, total, message) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            if (total > 0) {
                const percentage = (current / total) * 100;
                progressFill.style.width = `${percentage}%`;
                progressText.textContent = `${message} (${current}/${total})`;
            } else {
                progressText.textContent = message;
            }
        }

        function updatePRDOutput(content) {
            prdContent += content;
            document.getElementById('prdOutput').textContent = prdContent;
        }

        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const files = document.getElementById('files').files;
            const userInput = document.getElementById('userInput').value;
            const sessionId = document.getElementById('sessionId').value;
            const serverUrl = document.getElementById('serverUrl').value;

            // Add files to form data
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            if (userInput) {
                formData.append('user_input', userInput);
            }

            if (sessionId) {
                formData.append('session_id', sessionId);
            }

            // Reset UI
            prdContent = '';
            document.getElementById('prdOutput').textContent = 'PRD content will appear here as it\'s generated...';
            document.getElementById('logContainer').innerHTML = '';
            updateProgress(0, 0, 'Starting upload...');

            // Disable form
            document.getElementById('uploadBtn').disabled = true;
            updateStatus('Connecting to server...', 'connecting');

            // Make the request
            fetch(`${serverUrl}/api/v1/prd_upload/upload_files_stream`, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                updateStatus('Connected! Streaming data...', 'connected');
                log('Connected to streaming endpoint', 'success');

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            updateStatus('Stream completed', 'complete');
                            document.getElementById('uploadBtn').disabled = false;
                            return;
                        }

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.substring(6));
                                    handleStreamData(data);
                                } catch (e) {
                                    log(`JSON parse error: ${e.message}`, 'error');
                                }
                            }
                        }

                        return readStream();
                    });
                }

                return readStream();
            })
            .catch(error => {
                log(`Error: ${error.message}`, 'error');
                updateStatus(`Error: ${error.message}`, 'error');
                document.getElementById('uploadBtn').disabled = false;
            });
        });

        function handleStreamData(data) {
            switch (data.type) {
                case 'progress':
                    const stage = data.stage || 'unknown';
                    const message = data.message || '';
                    log(`[${stage.toUpperCase()}] ${message}`, 'progress');
                    
                    if (data.total_files && data.current_file) {
                        updateProgress(data.current_file, data.total_files, message);
                    } else {
                        updateProgress(0, 0, message);
                    }
                    break;

                case 'prd_chunk':
                    const content = data.content || '';
                    if (content) {
                        updatePRDOutput(content);
                        log(`PRD chunk received (${content.length} chars)`, 'prd');
                    }
                    if (data.is_final) {
                        log('PRD generation completed', 'success');
                    }
                    break;

                case 'result':
                    const resultData = data.data || {};
                    log(`Final result: Session ${resultData.session_id}`, 'success');
                    if (resultData.failed_files && resultData.failed_files.length > 0) {
                        log(`Failed files: ${resultData.failed_files.length}`, 'error');
                    }
                    break;

                case 'complete':
                    log(data.message || 'Processing completed', 'success');
                    updateStatus('Processing completed successfully!', 'complete');
                    document.getElementById('uploadBtn').disabled = false;
                    break;

                case 'error':
                    log(`Error: ${data.message}`, 'error');
                    updateStatus(`Error: ${data.message}`, 'error');
                    document.getElementById('uploadBtn').disabled = false;
                    break;

                default:
                    log(`Unknown message type: ${data.type}`, 'info');
            }
        }
    </script>
</body>
</html>
