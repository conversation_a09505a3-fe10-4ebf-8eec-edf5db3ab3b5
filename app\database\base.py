from sqlalchemy.ext.declarative import declarative_base
# from app.database.models import *  
Base = declarative_base()


def init_models():
    from app.database.models.user_model import User
    from app.database.models.plan_model import Plan
    from app.database.models.payment_model import Payment
    from app.database.models.subscriptions_model import Subscription
    from app.database.models.user_uploads_model import UserUpload
    from app.database.models.chat_session_model import ChatSession
    from app.database.models.chat_history_model import ChatHistory
    from app.database.models.guestusage import GuestUsage
    from app.database.models.billing_details import BillingDetails
    from app.database.models.notion_workspace_model import NotionToken
    from app.database.models.audit_logs_model import AuditLog
    from app.database.models.security_incident_model import SecurityIncident
    from app.database.models.content_page_model import ContentPage
    from app.database.models.atlassian_tokens_model import AtlassianToken
    from app.database.models.api_keys_settings_model import APIKeysSettings
    from app.database.models.user_session_model import UserSession
    from app.database.models.deletion_request_model import DeletionRequest
    from app.database.models.extracted_file_text_model import ExtractedFileText