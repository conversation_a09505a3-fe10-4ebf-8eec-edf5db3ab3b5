"""add ExtractedFileText table

Revision ID: b5b4224276fc
Revises: dbe4fbb459c7
Create Date: 2025-07-07 11:12:46.346690

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b5b4224276fc'
down_revision: Union[str, None] = 'dbe4fbb459c7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('extracted_file_texts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(), nullable=False),
    sa.Column('extracted_text', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_extracted_file_texts_id'), 'extracted_file_texts', ['id'], unique=False)
    op.create_index(op.f('ix_extracted_file_texts_session_id'), 'extracted_file_texts', ['session_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_extracted_file_texts_session_id'), table_name='extracted_file_texts')
    op.drop_index(op.f('ix_extracted_file_texts_id'), table_name='extracted_file_texts')
    op.drop_table('extracted_file_texts')
    # ### end Alembic commands ###
