import subprocess
import uvicorn
import os

if __name__ == "__main__":
    # celery_cmd = [
    #     "celery", "-A", "app.celery_app.celery_app", "worker",
    #     "--loglevel=info", "--pool=solo"
    # ]
    # env = os.environ.copy()
    # env["FLOWER_UNAUTHENTICATED_API"] = "true"
    # flower_cmd = [
    #     "celery", "-A", "app.celery_app.celery_app", "flower", "--port=5736"
    # ]

    # try:
    #     celery_process = subprocess.Popen(celery_cmd)
    #     flower_process = subprocess.Popen(flower_cmd, env=env)

        uvicorn.run("app.main:app", host="0.0.0.0", port=3044)  

    # except KeyboardInterrupt:
    #     print("Shutting down...")

    # finally:
    #     celery_process.terminate()
    #     flower_process.terminate()
    #     celery_process.wait()
    #     flower_process.wait()
