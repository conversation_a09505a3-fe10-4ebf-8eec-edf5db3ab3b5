{"cells": [{"cell_type": "code", "execution_count": 2, "id": "71fd2f32", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "# Create the persona identification prompt\n", "persona_prompt = PromptTemplate(\n", "    input_variables=[\"extracted_text\"],\n", "    template=\"\"\"\n", "You are an expert in business analysis and technical documentation.\n", "\n", "Your task is to identify the most relevant **professional persona** the AI assistant should adopt to generate a Project Requirement Document (PRD), based on the project context below.\n", "\n", "=== FORMAT REQUIREMENT ===\n", "Your response must strictly follow this structure:\n", "\"You are a [specific persona] with [X] years of experience\"\n", "\n", "=== EXAMPLES ===\n", "- You are a Healthcare IT Consultant with 10 years of experience  \n", "- You are an E-commerce AI Product Analyst with 15 years of experience  \n", "- You are a Logistics Optimization Specialist with 12 years of experience  \n", "- You are a Customer Experience AI Architect with 10 years of experience\n", "\n", "=== GUIDELINES ===\n", "✓ Persona should match the domain and industry context in the extracted text  \n", "✓ It must reflect the expertise required to analyze and structure the PRD  \n", "✓ Be as specific as possible (e.g., \"AI/ML Product Manager in FinTech\" is better than \"Product Manager\")\n", "\n", "=== PROJECT CONTEXT ===\n", "{extracted_text}\n", "\n", "Return only the persona string.\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "# Optional: save to file\n", "persona_prompt.save(\"persona_prompt.json\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "27871d73", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "myenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}