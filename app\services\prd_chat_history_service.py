from fastapi import HTTPException, status
from app.database.models.chat_history_model import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.database.models.chat_session_model import ChatSession
import json
from app.core.redis import redis_client
import re
from app.database.enum import RoleEnum

class PRDChatHistoryService:

    @staticmethod
    def get_chat_history(user_id : int, db, page: int = 1, page_size: int = 10):
        offset = (page - 1) * page_size
        total_sessions = db.query(ChatSession).filter(
            ChatSession.user_id == user_id
        ).count()

        sessions = db.query(ChatSession).filter(
            ChatSession.user_id == user_id
        ).order_by(ChatSession.created_at.desc()) \
        .offset(offset).limit(page_size).all()

        if not sessions:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="No chat history found for this page."
            )

        history = []

        for session in sessions:
            user_messages = db.query(ChatHistory).filter(
                ChatHistory.session_id == session.session_id,
                ChatHistory.role == "human"
            ).order_by(ChatHistory.created_at.asc()).limit(3).all()

            filenames = []
            for msg in user_messages:                
                image_matches = re.findall(r"!\[([^\]]+)\]\((?:/static/prd_image_uploads/.*?)\)", msg.message)
                filenames.extend(name.strip() for name in image_matches)

                lines = msg.message.splitlines()
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith("Uploaded files:") or re.match(r"!\[.*?\]\(.*?\)", line):
                        continue
                    filenames.append(line)

            history.append({
                "session_id": session.session_id,
                "created_at": session.created_at.isoformat(),
                "messages": filenames
            })

        result = {
            "page": page,
            "page_size": page_size,
            "sessions": history
        }
        return result


    @staticmethod
    def get_chat_data_by_chat_id(current_user, db, session_id):
        query = db.query(ChatHistory).join(
            ChatSession, ChatSession.session_id == ChatHistory.session_id
        ).filter(ChatHistory.session_id == session_id)

        if current_user.role != RoleEnum.Admin:
            query = query.filter(ChatSession.user_id == current_user.id)

        chat_data = query.order_by(ChatHistory.created_at.asc()).all()

        if not chat_data:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="No chat data found for this chat id."
            )
        serialized = []

        for row in chat_data:
            base_item = {
                "chat_id": row.chat_id,
                "session_id": row.session_id,
                "created_at": row.created_at.isoformat(),
                "role": row.role,
            }
            if row.role == "ai":
                base_item["message"] = row.message

            elif row.role == "human":
                filenames = []
                image_urls = []

                image_matches = re.findall(
                    r"!\[([^\]]+)\]\((/static/prd_image_uploads/[^\)]+)\)", row.message)
                for name, url in image_matches:
                    filenames.append(name.strip())
                    image_urls.append(url.strip())

                lines = row.message.splitlines()
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith("Uploaded files:") or re.match(r"!\[.*?\]\(.*?\)", line):
                        continue
                    filenames.append(line)

                base_item["message"] = filenames
                base_item["image_urls"] = image_urls

            serialized.append(base_item)
        return serialized


    @staticmethod
    def get_guest_user_chat_history(db, page: int = 1, page_size: int = 10):
        offset = (page - 1) * page_size
        total_sessions = db.query(ChatSession).filter(
            ChatSession.user_id == None
        ).count()
        sessions = db.query(ChatSession).filter(
            ChatSession.user_id == None
        ).order_by(ChatSession.created_at.desc()) \
        .offset(offset).limit(page_size).all()

        if not sessions:
            raise HTTPException(
                status_code=status.HTTP_203_NON_AUTHORITATIVE_INFORMATION,
                detail="No chat history found for this page."
            )
        history = []
        for session in sessions:
            user_messages = db.query(ChatHistory).filter(
                ChatHistory.session_id == session.session_id,
                ChatHistory.role == "human"
            ).order_by(ChatHistory.created_at.asc()).limit(3).all()

            messages = []
            for msg in user_messages:
                image_matches = re.findall(r"!\[([^\]]+)\]\((?:/static/prd_image_uploads/.*?)\)", msg.message)
                for name in image_matches:
                    messages.append(name.strip())
                lines = msg.message.splitlines()
                for line in lines:
                    line = line.strip()
                    if re.match(r"^!\[.*?\]\(.*?\)$", line):
                        continue
                    messages.append(line)
            clean_messages = [m for m in messages if m]
            history.append({
                "session_id": session.session_id,
                "created_at": session.created_at.isoformat(),
                "messages": clean_messages
            })
        result = {
            "page": page,
            "page_size": page_size,
            "sessions": history
        }
        return result
    




