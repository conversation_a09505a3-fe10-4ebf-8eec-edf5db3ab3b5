"""changes in billing_details

Revision ID: 94447622361e
Revises: 9e124b28edd3
Create Date: 2025-06-06 15:41:28.229234

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '94447622361e'
down_revision: Union[str, None] = '9e124b28edd3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('billing_details', sa.Column('address_line1', sa.String(), nullable=True))
    op.add_column('billing_details', sa.Column('address_line2', sa.String(), nullable=True))
    op.drop_column('billing_details', 'address')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('billing_details', sa.Column('address', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('billing_details', 'address_line2')
    op.drop_column('billing_details', 'address_line1')
    # ### end Alembic commands ###
