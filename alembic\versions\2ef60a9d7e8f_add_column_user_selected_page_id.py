"""add column user_selected_page_id

Revision ID: 2ef60a9d7e8f
Revises: c52741e5c6f2
Create Date: 2025-06-30 14:27:05.371860

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2ef60a9d7e8f'
down_revision: Union[str, None] = 'c52741e5c6f2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('notion_tokens', sa.Column('user_selected_page_id', sa.String(), nullable=True))
    op.drop_column('notion_tokens', 'raw_token_data')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('notion_tokens', sa.Column('raw_token_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_column('notion_tokens', 'user_selected_page_id')
    # ### end Alembic commands ###
