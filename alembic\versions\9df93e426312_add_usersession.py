"""add usersession

Revision ID: 9df93e426312
Revises: 838a75919302
Create Date: 2025-07-02 12:34:28.893306

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9df93e426312'
down_revision: Union[str, None] = '838a75919302'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_sessions',
    sa.Column('sessjtiion_id', sa.String(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.<PERSON>umn('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('sessjtiion_id')
    )
    op.create_index(op.f('ix_user_sessions_sessjtiion_id'), 'user_sessions', ['sessjtiion_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_sessions_sessjtiion_id'), table_name='user_sessions')
    op.drop_table('user_sessions')
    # ### end Alembic commands ###
