{"cells": [{"cell_type": "code", "execution_count": null, "id": "1fbcea9d", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "# Create the intelligent PRD template\n", "intelligent_prd_template = PromptTemplate(\n", "    input_variables=[\"extracted_text\", \"user_instruction\", \"persona\", \"input_token_count\", \"complexity_level\", \"target_token_range\"],\n", "    template=\"\"\"\n", "{persona}\n", "\n", "=== INTELLIGENT COMPLEXITY ANALYSIS ===\n", "\n", "You are tasked with generating a structured and professional Project Requirement Document (PRD) based on the extracted information and any optional user instructions.\n", "\n", "Estimated Input Token Count: **{input_token_count}**  \n", "Classified Complexity Level: **{complexity_level}**  \n", "Target Output Length: **{target_token_range} tokens**\n", "\n", "INPUT TO ANALYZE:\n", "Extracted Text:\n", "\\\"\\\"\\\"{extracted_text}\\\"\\\"\\\"\n", "\n", "User Instructions:\n", "\\\"\\\"\\\"{user_instruction}\\\"\\\"\\\"\n", "\n", "=== ANALYSIS FRAMEWORK ===\n", "\n", "Evaluate the input based on the following dimensions:\n", "\n", "1. **Information Richness**:  \n", "   - SPARSE: Minimal details, basic concept only  \n", "   - MODERATE: Some details provided, reasonable context  \n", "   - RICH: Comprehensive information with multiple aspects covered\n", "\n", "2. **Technical Complexity**:  \n", "   - SIMPLE: Basic functionality, straightforward implementation  \n", "   - MODERATE: Some technical challenges, integration needed  \n", "   - COMPLEX: Advanced technical requirements, multiple systems/technologies\n", "\n", "3. **Business Scope**:  \n", "   - NARROW: Single function/department, limited stakeholders  \n", "   - MEDIUM: Multiple functions, several stakeholder groups  \n", "   - BROAD: Enterprise-wide, complex stakeholder ecosystem\n", "\n", "4. **Requirement Specificity**:  \n", "   - VAGUE: General ideas, unclear requirements  \n", "   - MODERATE: Some specific requirements, reasonable clarity  \n", "   - DETAILED: Precise requirements, clear acceptance criteria\n", "\n", "=== COMPLEXITY LEVEL DETERMINATION ===\n", "\n", "Choose the appropriate level based on the analysis:\n", "\n", "- **MINIMAL**: Sparse/Simple/Narrow/Vague  \n", "- **BRIEF**: Somewhat moderate, limited detail  \n", "- **MODERATE**: Reasonably detailed, moderate challenge  \n", "- **DETAILED**: Rich input, complex systems, multiple stakeholders\n", "\n", "=== PRD GENERATION INSTRUCTIONS ===\n", "\n", "DO NOT include COMPLEXITY ANALYSIS in the PRD.\n", "\n", "FORMAT GUIDELINES:\n", "-  **All subheadings (e.g., Develop Dockerfiles)** must be bold and placed on a **separate line**  \n", "-  **Do NOT use any bullet points** like `-`, `•`, or `*`  \n", "-  Each subheading should be followed by a full sentence or paragraph on the **next line**  \n", "-  Apply this structure consistently across **all sections**\n", "-  Responses that include markdown-style bullets will be rejected\n", "\n", "**CRITICAL FORMATTING RULES:**\n", "- All section headings must be in **bold** and on separate lines\n", "- All subsection headings must be in **bold** and on separate lines\n", "- NO bullet points (-, •, *) anywhere in the document\n", "- Use numbered lists (1., 2., 3.) for sequential items only\n", "- Use descriptive paragraphs for all content\n", "- Each section must have substantial content matching the complexity level\n", "\n", "\n", "\n", "\n", "\n", "✓ Match your response to **{complexity_level}** complexity  \n", "✓ Keep your total output between **{target_token_range} tokens**\n", "✓ Output length is critical. You MUST ensure your response falls within the specified token range of {target_token_range} tokens. If your output is shorter than this, it is incomplete. \n", "Use all available insights from the input and follow the adaptive depth guidelines strictly.\n", "\n", "\n", "\n", "SECTION STRUCTURE:\n", "1. **Project Title**  \n", "2. **Project Overview**  \n", "3. **Business Objectives**  \n", "4. **Functional Requirements**  \n", "5. **Non-Functional Requirements**  \n", "6. **Stakeholders**  \n", "7. **Timeline and Milestones**  \n", "8. **Assumptions and Constraints**  \n", "9. **Risks and Mitigation**  \n", "10. **Conclusion and Next Steps**\n", "\n", "=== QUALITY STANDARDS ===\n", "\n", "✓ **Transparency**: Always explain your complexity reasoning  \n", "✓ **Consistency**: Ensure all sections match the selected complexity  \n", "✓ **Accuracy**: Do not invent facts; extrapolate only with logic  \n", "✓ **Clarity**: Mark vague or missing areas as \"To be determined\"  \n", "✓ **Professional Tone**: Use clear, concise, structured language  \n", "\n", "\n", "\n", "Now begin with your complexity analysis and proceed to PRD generation.\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "# Save the template\n", "intelligent_prd_template.save(\"prd_generation_template.json\")\n"]}, {"cell_type": "markdown", "id": "999abae0", "metadata": {}, "source": ["## FINAL PROMPT"]}, {"cell_type": "code", "execution_count": 12, "id": "e70543c0", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import PromptTemplate\n", "\n", "# Create the ultimate intelligent PRD template with enhanced visual hierarchy\n", "intelligent_prd_template = PromptTemplate(\n", "    input_variables=[\"extracted_text\", \"user_instruction\", \"persona\", \"input_token_count\", \"complexity_level\", \"target_token_range\"],\n", "    template=\"\"\"\n", "{persona}\n", "\n", "=== COMPREHENSIVE COMPLEXITY ANALYSIS ===\n", "\n", "You are tasked with generating a structured, professional, and industry-standard Project Requirement Document (PRD) based on the extracted information and user instructions.\n", "\n", "**Analysis Parameters:**\n", "Estimated Input Token Count: **{input_token_count}**  \n", "Classified Complexity Level: **{complexity_level}**  \n", "Target Output Length: **{target_token_range} tokens**  \n", "\n", "\n", "INPUT TO ANALYZE:\n", "Extracted Text:\n", "\\\"\\\"\\\"{extracted_text}\\\"\\\"\\\"\n", "\n", "User Instructions:\n", "\\\"\\\"\\\"{user_instruction}\\\"\\\"\\\"\n", "\n", "=== MULTI-DIMENSIONAL ANALYSIS FRAMEWORK ===\n", "\n", "Evaluate the input comprehensively across these seven critical dimensions:\n", "\n", "1. **Information Richness**:  \n", "   - SPARSE: Minimal details, basic concept only  \n", "   - MODERATE: Some details provided, reasonable context  \n", "   - RICH: Comprehensive information with multiple aspects covered\n", "\n", "2. **Technical Complexity**:  \n", "   - SIMPLE: Basic functionality, straightforward implementation  \n", "   - MODERATE: Some technical challenges, integration needed  \n", "   - COMPLEX: Advanced technical requirements, multiple systems/technologies\n", "\n", "3. **Business Scope**:  \n", "   - NARROW: Single function/department, limited stakeholders  \n", "   - MEDIUM: Multiple functions, several stakeholder groups  \n", "   - BROAD: Enterprise-wide, complex stakeholder ecosystem\n", "\n", "4. **Requirement Specificity**:  \n", "   - VAGUE: General ideas, unclear requirements  \n", "   - MODERATE: Some specific requirements, reasonable clarity  \n", "   - DETAILED: Precise requirements, clear acceptance criteria\n", "\n", "5. **Stakeholder Complexity**:\n", "   - SIMPLE: 1-2 stakeholder types, clear alignment\n", "   - MODERATE: 3-5 stakeholder groups, some competing interests\n", "   - COMPLEX: 6+ stakeholder groups with conflicting needs and priorities\n", "\n", "6. **Integration Complexity**:\n", "   - MINIMAL: Standalone system, no external dependencies\n", "   - MODERATE: 2-3 system integrations, standard APIs\n", "   - COMPLEX: Multiple system integrations, custom APIs, third-party services\n", "\n", "7. **Compliance and Risk Level**:\n", "   - LOW: No regulatory requirements, standard business risk\n", "   - MODERATE: Industry-standard compliance, moderate risk factors\n", "   - HIGH: Multiple compliance frameworks, high-risk environment\n", "\n", "=== COMPLEXITY LEVEL DETERMINATION ===\n", "\n", "Based on the multi-dimensional analysis, select the appropriate complexity level:\n", "\n", "- **MINIMAL**: Predominantly Sparse/Simple/Narrow/Vague characteristics (3-4 dimensions at lowest level)\n", "- **BRIEF**: Mixed characteristics with moderate elements (2-3 dimensions at moderate level)  \n", "- **MODERATE**: Predominantly moderate characteristics across most dimensions (4-5 dimensions at moderate level)\n", "- **DETAILED**: Multiple complex characteristics or high-stakes environment (3+ dimensions at complex/high level)\n", "\n", "=== PRD GENERATION INSTRUCTIONS ===\n", "\n", "DO NOT include COMPLEXITY ANALYSIS in the PRD.\n", "\n", "=== CRITICAL FORMATTING REQUIREMENTS ===\n", "\n", "**MA<PERSON><PERSON>ORY FORMATTING RULES - VIOLATIONS WILL RESULT IN REJECTION:**\n", "\n", "- **MAIN TITLE** must use **# heading** (largest font size)\n", "- **All section headings** must be **numbered and bold** using **## format** and placed on a **separate line**\n", "- **All subsection headings** must use **ENHANCED VISUAL HIERARCHY** with the following format:\n", "  - Use **## format for main section headings**: ## **X. Section Name**\n", "  - Use **bold formatting with 8-space indentation for subheadings**: **        X.Y Subsection Name**\n", "  - Content under subsections should start with 8 spaces for visual alignment\n", "  - Add proper spacing and indentation to create clear visual hierarchy\n", "- **ABSOLUTELY NO bullet points** using `-`, `•`, or `*` anywhere in the document\n", "- **NO markdown-style bullets** will be accepted under any circumstances\n", "- Use **numbered lists (1., 2., 3.)** for sequential items ONLY when absolutely necessary\n", "- Each heading must be followed by a **full sentence or paragraph** on the **next line**\n", "- Apply this structure **consistently across ALL sections**\n", "- Use **descriptive paragraphs** for all content\n", "- Each section must have **substantial content** matching the complexity level\n", "\n", "**ENHANCED HEADING HIERARCHY:**\n", "- **Main Title**: # Project Requirement Document (PRD) for [Project Name]\n", "- **Section Headings**: ## **1. Executive Summary**\n", "- **Subsection Headings with Visual Hierarchy**: \n", "  **        1.1 Problem Overview**\n", "  Content for this subsection goes here with proper indentation and spacing.\n", "  **        1.2 Proposed Solution**\n", "  Content for this subsection goes here with proper indentation and spacing.\n", "\n", "**VISUAL HIERARCHY EXAMPLE:**\n", "```\n", "## **17. Post-Launch Strategy and Evolution**\n", "**        17.1 Maintenance Strategy**\n", "        Scheduled maintenance windows for software updates and data security reinforcement coupled with user notification strategies.\n", "**        17.2 Performance Monitoring**\n", "        Leveraging analytics tools to monitor user engagement and system performance metrics, ensuring continuous improvements and adjustments.\n", "**        17.3 Future Enhancements**\n", "        Planning additional features like custom reporting tools, more integration capabilities, and enhanced AI modeling as future platform upgrades based on user feedback and competitive analysis.\n", "```\n", "\n", "=== ADAPTIVE DEPTH GUIDELINES ===\n", "\n", "**MINIMAL Complexity (500-800 tokens):**\n", "- Brief paragraphs of 2-3 sentences each\n", "- Essential sections only with high-level overview\n", "- Basic requirements without extensive detail\n", "- Primary stakeholder identification\n", "- Simplified timeline and risk assessment\n", "\n", "**BRIEF Complexity (800-1200 tokens):**\n", "- Moderate paragraphs of 3-4 sentences each\n", "- Standard sections with reasonable detail\n", "- Specific requirements with basic context\n", "- Clear stakeholder roles and responsibilities\n", "- Detailed timeline with key milestones\n", "\n", "**MODERATE Complexity (1200-2000 tokens):**\n", "- Detailed paragraphs of 4-6 sentences each\n", "- All sections with substantial content and analysis\n", "- Comprehensive requirements with business rationale\n", "- Complete stakeholder analysis with interaction mapping\n", "- Detailed project planning with dependencies\n", "\n", "**DETAILED Complexity (2000+ tokens):**\n", "- Comprehensive paragraphs of 6+ sentences each\n", "- All sections with extensive detail and strategic analysis\n", "- Exhaustive requirements with full business justification\n", "- Complete stakeholder ecosystem with influence mapping\n", "- Comprehensive project planning with risk mitigation strategies\n", "\n", "✓ Match your response to **{complexity_level}** complexity  \n", "✓ Keep your total output between **{target_token_range} tokens** strictly \n", "✓ Output length is critical. You MUST ensure your response falls within the specified token range of {target_token_range} tokens. If your output is shorter than this, it is incomplete. \n", "Use all available insights from the input and follow the adaptive depth guidelines strictly.\n", "\n", "=== ENHANCED SECTION STRUCTURE ===\n", "\n", "Generate a PRD with these sections, ensuring each section provides value and depth appropriate to the complexity level:\n", "\n", "**1. Executive Summary**\n", "Provide a concise yet comprehensive overview including the core problem statement, proposed solution approach, key business benefits, primary success metrics, and high-level timeline with major milestones.\n", "\n", "**2. Problem Statement and Business Context**\n", "Clearly articulate the specific business problem or market opportunity, conduct current state analysis, provide relevant market context, and establish the business case that justifies this project investment.\n", "\n", "**3. Goals and Success Metrics**\n", "Define specific, measurable, achievable, relevant, and time-bound objectives along with key performance indicators (KPIs), success criteria, and measurement methodologies that will determine project success.\n", "\n", "**4. <PERSON>r <PERSON>as and Use Cases**\n", "Identify and describe primary and secondary user personas, their characteristics, pain points, motivations, and specific use cases they will engage with throughout their journey.\n", "\n", "**5. Functional Requirements**\n", "Detail precisely what the system must accomplish, organized by feature categories or user stories, with clear acceptance criteria and business rules that govern system behavior.\n", "*Intelligently identify and create subheadings based on actual functional areas mentioned in the input (e.g., user management, data processing, reporting, workflow automation, etc.)*\n", "\n", "**6. Technical Requirements**\n", "Specify technical architecture decisions, required platforms and technologies, performance benchmarks, system capabilities, and technical constraints that must be considered.\n", "*Intelligently identify and create subheadings based on actual technical aspects mentioned in the input (e.g., Backend Technologies like FastAPI/Python, Frontend Technologies like React/Vue, Database like PostgreSQL/MongoDB, Cloud Infrastructure like AWS/Azure, AI/ML Components, etc.)*\n", "\n", "**7. Non-Functional Requirements**\n", "Address critical quality attributes including scalability thresholds, performance standards, security measures, usability guidelines, reliability expectations, and maintainability requirements.\n", "*Create subheadings only for aspects that are relevant to the project (e.g., Performance & Scalability if high-traffic, Security Requirements if sensitive data, Usability if user-facing, etc.)*\n", "\n", "**8. User Interface and Experience Requirements**\n", "Define UI/UX design standards, accessibility compliance requirements, user interaction patterns, and design principles that will guide the user experience.\n", "*Create subheadings only if the project involves UI/UX elements (e.g., Web Interface Design, Mobile App Design, Dashboard Requirements, Accessibility Standards, etc.)*\n", "\n", "**9. Integration and API Requirements**\n", "Specify required integrations with existing systems, third-party services, API specifications, data exchange formats, and integration architecture decisions.\n", "*Create subheadings only if integrations are mentioned in the input (e.g., Third-Party API Integration like Payment Gateways, Social Media APIs, Database Integration, Legacy System Integration, etc.)*\n", "\n", "**10. Security and Compliance Framework**\n", "Detail comprehensive security measures, data protection protocols, privacy requirements, and regulatory compliance needs specific to the industry and geographic context.\n", "*Create subheadings based on actual security/compliance needs mentioned (e.g., Data Encryption, Authentication Systems, GDPR Compliance, Healthcare Compliance, Financial Regulations, etc.)*\n", "\n", "**11. Stakeholder Analysis and Communication**\n", "Identify all project stakeholders, their roles and responsibilities, decision-making authority, communication preferences, and engagement strategies throughout the project lifecycle.\n", "*Create subheadings based on actual stakeholder types identified (e.g., Internal Stakeholders, External Partners, End Users, Management Team, Development Team, etc.)*\n", "\n", "**12. Project Timeline and Milestone Framework**\n", "Provide detailed project phases, critical milestones, dependencies between tasks, resource allocation timelines, and key decision points that will guide project execution.\n", "*Create subheadings based on actual project phases mentioned (e.g., Discovery Phase, Development Phase, Testing Phase, Deployment Phase, specific milestones, etc.)*\n", "\n", "**13. Resource Requirements and Budget Considerations**\n", "Specify required team composition, necessary skills and expertise, infrastructure requirements, technology investments, and high-level budget considerations for successful delivery.\n", "*Create subheadings based on actual resource needs mentioned (e.g., Development Team Requirements, Infrastructure Costs, Software Licenses, Hardware Requirements, etc.)*\n", "\n", "**14. Risk Assessment and Mitigation Strategies**\n", "Identify potential risks across technical, business, and operational dimensions, assess their probability and impact, and define specific mitigation strategies and contingency plans.\n", "*Create subheadings based on actual risk categories relevant to the project (e.g., Technical Risks, Market Risks, Resource Risks, Timeline Risks, etc.)*\n", "\n", "**15. Assumptions and Dependencies**\n", "List critical assumptions underlying the project plan, external dependencies that could affect delivery, and validation approaches for key assumptions.\n", "*Create subheadings based on actual assumptions and dependencies mentioned (e.g., Technology Assumptions, Business Assumptions, External Dependencies, Internal Dependencies, etc.)*\n", "\n", "**16. Acceptance Criteria and Quality Assurance**\n", "Define comprehensive success validation criteria, testing approaches, quality gates, and acceptance processes that will ensure deliverables meet requirements.\n", "*Create subheadings based on actual testing/QA needs (e.g., Functional Testing, Performance Testing, Security Testing, User Acceptance Testing, etc.)*\n", "\n", "**17. Post-Launch Strategy and Evolution**\n", "Address ongoing maintenance requirements, support model, scaling considerations, performance monitoring, and future enhancement planning beyond initial delivery.\n", "*Create subheadings based on actual post-launch needs mentioned (e.g., Maintenance Strategy, Support Model, Performance Monitoring, Future Enhancements, etc.)*\n", "\n", "=== QUALITY EXCELLENCE STANDARDS ===\n", "\n", "✓ **Professional Communication**: Use clear, concise, business-appropriate language that stakeholders can understand and act upon\n", "✓ **Logical Architecture**: Ensure each section builds logically upon previous sections and contributes to overall document coherence\n", "✓ **Precision and Specificity**: Avoid vague statements and generalizations; provide specific, actionable information wherever possible\n", "✓ **Requirements Traceability**: Ensure all requirements can be traced back to business objectives and user needs\n", "✓ **Technical Feasibility**: Consider realistic technical and business constraints in all recommendations\n", "✓ **Comprehensive Coverage**: Address all aspects relevant to the determined complexity level without unnecessary elaboration\n", "✓ **Terminology Consistency**: Maintain consistent use of terms, definitions, and formatting throughout the entire document\n", "✓ **Stakeholder Value**: Focus on creating content that enables informed decision-making and successful project execution\n", "\n", "=== EXECUTION IMPERATIVES ===\n", "\n", "**CRITICAL SUCCESS FACTORS:**\n", "- Your response MUST fall within the {target_token_range} token range\n", "- Content depth and detail MUST match the {complexity_level} exactly\n", "- When specific information is unavailable, clearly state \"To be determined\" rather than creating fictional details\n", "- Focus on creating a document that stakeholders can immediately use for decision-making and project planning\n", "- Ensure every section provides genuine value and insights appropriate to the complexity level\n", "- **APPLY ENHANCED VISUAL HIERARCHY** throughout the document using the specified formatting\n", "\n", "**DO NOT include the complexity analysis in the final PRD output.**\n", "\n", "**OUTPUT LENGTH IS CRITICAL:** You MUST ensure your response falls within the specified token range of {target_token_range} tokens. If your output is shorter than this range, it is considered incomplete. Use all available insights from the input and follow the adaptive depth guidelines strictly to achieve the target length.\n", "\n", "**PRD DOCUMENT STRUCTURE:**\n", "Begin your PRD with the following title format:\n", "# Project Requirement Document (PRD) for [Project Name]\n", "\n", "Then proceed with the numbered sections below using the enhanced heading hierarchy with proper visual spacing and indentation.\n", "\n", "**ENHANCED HEADING FORMAT EXAMPLES:**\n", "Main Title: # Project Requirement Document (PRD) for [Project Name]\n", "Section Headings: ## **1. Executive Summary**\n", "Subsection Headings: **        1.1 Problem Overview**\n", "\n", "**MANDATORY SUBHEADING USAGE WITH VISUAL HIERARCHY:**\n", "- Sections 1-4: Use subheadings only when complexity level is  DETAILED\n", "- Sections 5-17: **INTELLIGENTLY CREATE SUBHEADINGS** with enhanced visual hierarchy based on actual content and context from the input\n", "- **SMART SUBHEADING CREATION RULES:**\n", "  - Only create subheadings for aspects that are actually mentioned or relevant to the project\n", "  - Use specific, contextual subheading names that reflect the actual content (e.g., \"FastAPI Backend Development\" instead of generic \"Backend Requirements\")\n", "  - If a section has minimal content, don't force subheadings - use flowing paragraphs instead\n", "  - For technical sections, use actual technology names mentioned in the input (e.g., \"React Frontend Components\", \"PostgreSQL Database Design\", \"AWS Cloud Infrastructure\")\n", "  - For functional sections, use actual feature names from the input (e.g., \"User Authentication System\", \"Payment Processing Module\", \"Reporting Dashboard\")\n", "  - **APPLY CONSISTENT VISUAL HIERARCHY** using the format: **        X.Y Subheading Name**\n", "  - All subheadings must be bold and use exactly 8 spaces for indentation\n", "  - Content under subheadings should be indented with 8 spaces to maintain visual alignment\n", "- Adapt subheading content depth based on complexity level\n", "- Each subsection must contain substantial content appropriate to the complexity level\n", "\n", "**CRITICAL FORMATTING RULES FOR SUBHEADINGS:**\n", "- Main section headings: ## **X. Section Name**\n", "- Subsection headings: **        X.Y Subsection Name** (bold with 8 spaces indentation)\n", "- Content under subsections: Start with 8 spaces indentation for alignment\n", "- Ensure all subheadings are bold and consistently formatted\n", "\n", "Now begin with your complexity analysis and proceed to PRD generation following these enhanced guidelines with proper visual hierarchy, ensuring professional quality and stakeholder value throughout.\n", "\"\"\",\n", "    validate_template=True\n", ")\n", "\n", "# Save the enhanced template\n", "intelligent_prd_template.save(\"prd_generation_template.json\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}