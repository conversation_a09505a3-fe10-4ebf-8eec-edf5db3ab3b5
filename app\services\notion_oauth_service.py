from app.core.config import settings
from app.database.session import get_db
from fastapi import Depends, Request,HTTPException
from sqlalchemy.orm import Session
import httpx
from app.database.models.notion_workspace_model import NotionToken
import base64
import requests
import json
from fastapi.responses import RedirectResponse
from app.services.api_keys_settings_service import APIKEYSETTINGService
from app.database.enum import KeyEnum



class NotionOauthService:

    @staticmethod
    async def notion_oauth_service(request: Request,current_user, db: Session = Depends(get_db)):
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.NOTION_API_KEYS)
        notion_auth_url = key["NOTION_AUTHORIZATION_URL"]
        if notion_auth_url:
            Notion_Authorization_URL= notion_auth_url
        else:
            Notion_Authorization_URL = settings.NOTION_AUTHORIZATION_URL
        user_id = current_user.id        
        user_data = {"user_id": user_id}
        encoded_state = base64.b64encode(json.dumps(user_data).encode()).decode()
        auth_url = f"{Notion_Authorization_URL}&state={encoded_state}"
        return {"auth_url": auth_url}
    

    @staticmethod
    async def notion_callback_service(request: Request, db: Session = Depends(get_db)):
        key = APIKEYSETTINGService.get_api_key_by_name(db, KeyEnum.NOTION_API_KEYS)
        motion_oauth_client_id = key["NOTION_OAUTH_Client_ID"]
        motion_oauth_client_secret = key["NOTION_OAuth_Client_Secret"]
        if motion_oauth_client_id and motion_oauth_client_secret:
            NOTION_OAUTH_Client_ID= motion_oauth_client_id
            NOTION_OAuth_Client_Secret= motion_oauth_client_secret
        else:
            NOTION_OAUTH_Client_ID = settings.NOTION_OAUTH_Client_ID
            NOTION_OAuth_Client_Secret = settings.NOTION_OAuth_Client_Secret
        code = request.query_params.get("code")
        state = request.query_params.get("state")
        if not code:
            raise HTTPException(status_code=400, detail="Missing 'code' in query parameters.")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.notion.com/v1/oauth/token",
                auth=(NOTION_OAUTH_Client_ID, NOTION_OAuth_Client_Secret),
                headers={"Content-Type": "application/json"},
                json={
                    "grant_type": "authorization_code",
                    "code": code,
                    "redirect_uri": settings.NOTION_REDIRECT_URI,
                },
            )

            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=response.text)           


            token_data = response.json()
            access_token = token_data.get("access_token")
            workspace_id = token_data.get("workspace_id")
            bot_id = token_data.get("bot_id")


            decoded_data = json.loads(base64.b64decode(state.encode()).decode())
            user_id = decoded_data.get("user_id")
            template_id = await NotionOauthService.create_template_page(access_token)
        
            token_entry = NotionToken(
                workspace_id=workspace_id,
                access_token=access_token,
                bot_id=bot_id,
                duplicated_template_id=template_id,
                user_id=user_id
            )
            db.merge(token_entry)
            db.commit()
            message="OAuth token received and saved successfully!"
            return RedirectResponse(url=f"{settings.FRONTEND_URL}/success?message={message}")
        
    

    @staticmethod
    async def create_template_page(access_token: str):
        """Create a template page that can be used as parent for new pages"""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Notion-Version": settings.NOTION_VERSION,
            "Content-Type": "application/json"
        }
        async with httpx.AsyncClient() as client:
            search_response = await client.post(
                "https://api.notion.com/v1/search",
                headers=headers,
                json={
                    "filter": {
                        "value": "page",
                        "property": "object"
                    }
                }
            )
            
            if search_response.status_code != 200:
                raise HTTPException(status_code=search_response.status_code, detail="Failed to search pages")
            
            search_data = search_response.json()
            
            parent_page_id = None
            if search_data.get("results"):
                parent_page_id = search_data["results"][0]["id"]
            
            if not parent_page_id:
                raise HTTPException(status_code=400, detail="No accessible parent page found")
            
            template_payload = {
                "parent": {"page_id": parent_page_id},
                "properties": {
                    "title": [
                        {
                            "type": "text",
                            "text": {"content": "Template - Text Content"}
                        }
                    ]
                }                
            }
            
            template_response = await client.post(
                "https://api.notion.com/v1/pages",
                headers=headers,
                json=template_payload
            )
            
            if template_response.status_code != 200:
                raise HTTPException(status_code=template_response.status_code, detail="Failed to create template")
            
            template_data = template_response.json()
            return template_data["id"]
        
    @staticmethod
    def send_text_to_notion(access_token: str, parent_page_id: str, title: str, text: str):
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Notion-Version": settings.NOTION_VERSION,
            "Content-Type": "application/json"
        }

        paragraphs = [p.strip() for p in text.split("\n") if p.strip()]

        children = [
            {
                "object": "block",
                "type": "paragraph",
                "paragraph": {
                    "rich_text": [
                        {
                            "type": "text",
                            "text": {
                                "content": para[:2000]  
                            }
                        }
                    ]
                }
            } for para in paragraphs[:100] 
        ]

        payload = {
            "parent": {"page_id": parent_page_id},
            "properties": {
                "title": [
                    {
                        "type": "text",
                        "text": {"content": title}
                    }
                ]
            },
            "children": children
        }

        res = requests.post("https://api.notion.com/v1/pages", headers=headers, json=payload)
        if res.status_code != 200:
            raise HTTPException(status_code=res.status_code, detail="Failed to send text to Notion")
        else:
            return {"message": "Text sent to Notion successfully!"}