from fastapi import APIRouter, Depends, UploadFile, File,HTTPException,status,Request,Form
from fastapi.responses import StreamingResponse
from typing import List, AsyncGenerator
from app.database.session import get_db
from app.database.dependencies import optional_get_current_user
from app.services.prd_upload_service import PrdUploadService
from sqlalchemy.orm import Session
from typing import Optional
from app.database.models.user_model import User
import json
import asyncio


router = APIRouter()


@router.post("/upload_files")
def process_uploaded_files(request: Request,user_input: Optional[str] = Form(None),current_user: Optional[User] = Depends(optional_get_current_user),
                           files: List[UploadFile] = File(None),session_id: Optional[str] = None,db: Session = Depends(get_db)):
    try:
        client_ip = request.client.host

        if not current_user:
            PrdUploadService.handle_guest_user(db, client_ip)
        else:
           PrdUploadService.validate_authenticated_user(db, current_user)

        session_id = PrdUploadService.handle_session(db, session_id, current_user, files)

        result = PrdUploadService.process_uploaded_files(
            user_id=current_user.id if current_user else None,
            user_input=user_input,
            files=files,
            session_id=session_id,
            db=db
        )

        if current_user and current_user.role.name == "User":
            PrdUploadService.decrement_user_upload_count(db, current_user)

        return {"message": "Files processed successfully", "data": result}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing files: {str(e)}"
        )

@router.post("/upload_files_stream")
async def process_uploaded_files_stream(
    request: Request,
    user_input: Optional[str] = Form(None),
    current_user: Optional[User] = Depends(optional_get_current_user),
    files: List[UploadFile] = File(None),
    session_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Stream file processing with real-time progress updates via Server-Sent Events
    """
    async def generate_stream() -> AsyncGenerator[str, None]:
        try:
            client_ip = request.client.host

            # Validation phase
            yield f"data: {json.dumps({'type': 'progress', 'stage': 'validation', 'message': 'Validating user permissions...'})}\n\n"
            await asyncio.sleep(0.1)  # Small delay for UI responsiveness

            if not current_user:
                PrdUploadService.handle_guest_user(db, client_ip)
            else:
                PrdUploadService.validate_authenticated_user(db, current_user)

            yield f"data: {json.dumps({'type': 'progress', 'stage': 'session', 'message': 'Setting up session...'})}\n\n"
            await asyncio.sleep(0.1)

            session_id = PrdUploadService.handle_session(db, session_id, current_user, files)

            # Process files with streaming
            yield f"data: {json.dumps({'type': 'progress', 'stage': 'processing', 'message': 'Starting file processing...'})}\n\n"
            await asyncio.sleep(0.1)

            async for chunk in PrdUploadService.process_uploaded_files_stream(
                user_id=current_user.id if current_user else None,
                user_input=user_input,
                files=files,
                session_id=session_id,
                db=db
            ):
                yield f"data: {json.dumps(chunk)}\n\n"
                await asyncio.sleep(0.01)  # Small delay to prevent overwhelming the client

            # Decrement user upload count after successful processing
            if current_user and current_user.role.name == "User":
                PrdUploadService.decrement_user_upload_count(db, current_user)

            yield f"data: {json.dumps({'type': 'complete', 'message': 'Processing completed successfully'})}\n\n"

        except HTTPException as e:
            yield f"data: {json.dumps({'type': 'error', 'message': e.detail})}\n\n"
        except Exception as e:
            yield f"data: {json.dumps({'type': 'error', 'message': f'Error processing files: {str(e)}'})}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )
